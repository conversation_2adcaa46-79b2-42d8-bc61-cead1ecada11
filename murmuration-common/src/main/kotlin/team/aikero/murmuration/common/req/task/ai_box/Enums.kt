package team.aikero.murmuration.common.req.task.ai_box

enum class TaskSource {
    /**
     * AI_BOX
     */
    AI_BOX,

    /**
     * AI满印
     */
    AI_FULL_SEAL,

    /**
     * 视觉中心
     */
    VISUAL_CENTER,

    /**
     * 商品中心
     */
    COMMODITY_CENTER,

    /**
     * AI时装 选款
     */
    AI_FASHION_SELECTION,

    /**
     * AI时装 选款结果
     */
    AI_FASHION_SELECTION_RESULT,

    /**
     * AI现货
     */
    AI_STOCK,

    /**
     * AI POD
     */
    AI_POD
}

enum class CuttingSize {
    /**
     * 1:1
     */
    OneToOne,

    /**
     * 3:4
     */
    ThreeToFour;

    fun getSize(): String =
        when (this) {
            OneToOne -> "1400x1400"
            ThreeToFour -> "900x1200"
        }

}

enum class TopLoc {
    /**
     * 下巴
     */
    JAW,

    /**
     * 鼻梁
     */
    BRIDGE_NOSE,

    /**
     * 鼻尖
     */
    TIP_NOSE,

    /**
     * 脖子
     */
    NECK
}