package team.aikero.murmuration.common.vo

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.treeToValue
import team.aikero.blade.util.json.Json

/**
 * 统一的任务结果结构
 *
 * 支持url和attributes的可选组合，为了解决url和attributes可空的情况
 *
 * <AUTHOR>
 */
data class SimpleTaskResult(
    /**
     * 任务ID
     */
    val taskId: Long,

    /**
     * 图片URL（可选）
     * 当任务结果包含图片时使用
     */
    val url: String? = null,

    /**
     * 任务结果属性（可选）
     * 当任务结果包含结构化数据时使用
     */
    val attributes: JsonNode? = null,
) {
    init {
        require(url != null || attributes != null) {
            "任务结果必须包含url或attributes中的至少一项"
        }
    }

    /**
     * 安全获取URL（如果存在）
     */
    fun fetchUrl(): String = url ?: throw IllegalArgumentException("URL结果不存在")

    /**
     * 将属性转换为指定类型
     */
    inline fun <reified T> getAttributesAs(): T? {
        return attributes?.let(Json.instance::treeToValue)
    }

    /**
     * 将属性转换为指定类型（兼容Java）
     */
    fun <T> getAttributesAs(clazz: Class<T>): T? {
        return attributes?.let { Json.instance.treeToValue(it, clazz) }
    }

    /**
     * 将属性转换为指定类型（兼容Java）
     */
    fun <T> getAttributesAs(clazz: TypeReference<T>): T? {
        return attributes?.let { Json.instance.treeToValue(it, clazz) }
    }
}
