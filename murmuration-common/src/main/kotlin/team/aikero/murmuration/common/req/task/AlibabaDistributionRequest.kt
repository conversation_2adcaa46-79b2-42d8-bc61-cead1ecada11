package team.aikero.murmuration.common.req.task

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/8/26 17:08
 */
data class AlibabaDistributionRequest(

    /**
     * SPU轮播图URLs
     */
    val spuCarouselUrls: List<String>,

    /**
     * SPU详情图URLs
     */
    val spuDetailUrls: List<String>,

    /**
     * SKC图片URLs
     */
    val skcInfos: List<SkcInfo>,
)

data class SkcInfo(
    @field:JsonProperty("skc_url")
    var skcUrl : String,
    @field:JsonProperty("design_code")
    var designCode : String
)