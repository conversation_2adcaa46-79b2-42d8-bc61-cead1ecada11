package team.aikero.murmuration.common.req.task

/**
 * SKC颜色识别请求
 */
data class SkcColorIdentificationRequest(
    /**
     * 图片URL
     */
    val url: String,

    /**
     * 品类（内部品类，多级，用-拼接），参考：女装-上装类-卫衣帽衫
     */
    val categoryName: String,

    /**
     * 输出图片URL
     */
    val outputUrl: String?,

    /**
     * SPU ID（spu-code）参考：PG2508230005
     */
    val spuId: String,

    /**
     * SKC ID（skc-code）参考：PS250823000030
     */
    val skcId: String,

    /**
     * SKC名称, 原商品颜色名，参考：卡其色
     */
    val skcName: String,
)