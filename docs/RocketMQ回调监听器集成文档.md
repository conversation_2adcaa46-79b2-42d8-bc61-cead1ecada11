# RocketMQ 回调监听器集成文档

## 概述

本文档详细介绍了 Murmuration 系统中两种 RocketMQ 回调监听器的实现机制：`ApiTaskListener` 和 `AIBoxTaskListener`。这两个监听器负责在任务状态变更时发送 MQ 消息通知，为不同的业务场景提供异步回调能力。

## 1. 监听器实现详解

### 1.1 ApiTaskListener 实现

`ApiTaskListener` 专门处理通过 API 调用触发的任务状态通知。

#### 核心特性
- **触发条件**: 仅处理 `TriggerSource.API` 类型的任务
- **监听事件**: `TaskEvent` 任务完成事件
- **消息发送**: 任务达到终结状态时发送通知
- **消息结构**: 包含任务详细信息和执行结果

#### 消息结构
```kotlin
data class TaskStatusNotification(
    val taskId: Long,           // 任务ID
    val bizId: String,          // 业务ID
    val bizType: String,        // 业务类型
    val ability: Ability,       // AI能力
    val supplier: Supplier,     // 供应商
    val status: TaskStatus,     // 任务状态
    val results: List<SimpleTaskResult>  // 执行结果
)
```

### 1.2 AIBoxTaskListener 实现

`AIBoxTaskListener` 处理 AI Box 相关的任务状态通知，支持多任务聚合和分布式锁机制。

#### 核心特性
- **触发条件**: 仅处理 `TriggerSource.AI_BOX` 类型的任务
- **多事件监听**: 支持 `TaskPrepareEvent`、`TaskEvent`、`TaskCompletedEvent`
- **聚合逻辑**: 等待所有子任务完成后统一发送通知
- **分布式锁**: 使用 Redisson 防止并发状态更新冲突
- **特殊回调**: 支持背景图片提示词回调功能

#### 消息结构

```kotlin
data class AIBoxTaskNotification(
    val aiBoxTaskId: Long,      // AI Box任务ID
    val ability: Ability,       // AI能力
    val supplier: Supplier,     // 供应商
    val taskSourceId: String,   // 任务源ID
    val status: TaskStatus,     // 任务状态
    val results: List<SimpleTaskResult> = emptyList()  // 聚合结果
)
```

## 2. 消息发送对比

| 特性 | ApiTaskListener | AIBoxTaskListener |
|------|----------------|------------------|
| **触发源** | TriggerSource.API | TriggerSource.AI_BOX |
| **Topic** | `${env}_murmuration_task_status_notification` | `${env}_murmuration_ai_box_task_status_notification` |
| **Message Key** | 任务实例ID | AI Box任务ID |
| **Message Tag** | 业务类型 (bizType) | 任务来源 (taskSource) |
| **发送时机** | 单任务完成即发送 | 所有子任务完成后发送 |
| **并发控制** | 无需特殊处理 | 使用分布式锁 |
| **结果聚合** | 单任务结果 | 多任务结果聚合 |

## 3. 集成指南

### 3.1 环境准备

#### 依赖配置
```kotlin
// build.gradle.kts
dependencies {
    implementation("org.apache.rocketmq:rocketmq-v5-client-spring-boot-starter:2.3.3")
}
```

#### 配置文件

```yaml
rocketmq:
  simple-consumer:
    endpoints: ${mqs.rocketmq.default.name-server}
    accessKey: ${mqs.rocketmq.default.access-key}
    secretKey: ${mqs.rocketmq.default.secret-key}
    tag: "*"
  push-consumer:
    endpoints: ${mqs.rocketmq.default.name-server}
    accessKey: ${mqs.rocketmq.default.access-key}
    secretKey: ${mqs.rocketmq.default.secret-key}
    tag: "*"
```

#### 监听器注册

以 AIBox 回调示例，监听器通过 Spring 的 `@Component` 和 `@RocketMQMessageListener` 注解自动注册：

```kotlin
@Component
@RocketMQMessageListener(
    topic = "\${env}_murmuration_ai_box_task_status_notification",
    consumerGroup = "\${env}_your_application_service_name",
    tag = "*",
)
class AIBoxTaskListener : RocketMQListener {
    override fun consume(messageView: MessageView): ConsumeResult {
        try {
            log.debug { "ai box created message keys ${messageView.keys}" }
            log.debug { "ai box message: $messageView" }
            val body = messageView.body.toByteString().string(StandardCharsets.UTF_8)
            log.debug { "ai box message body: $body" }
            val taskStatusNotification: AIBoxTaskFinishNotification = body.parseJson()

            taskStatusNotification.ability
            return when (taskStatusNotification.status) {
                COMPLETED -> TODO()
                FAILED -> TODO()
                TaskStatus.RUNNING -> TODO()
                else -> return ConsumeResult.SUCCESS
            }
        } catch (ex: Exception) {
            log.error(ex) { "ai box message error" }
            return ConsumeResult.FAILURE
        }
    }

}

data class AIBoxTaskFinishNotification(
    val aiBoxTaskId: Long,
    val ability: Ability,
    val supplier: String,
    val taskSourceId: String,
    val status: TaskStatus,
    val results: List<AIBoxMQTaskResult>,
)

data class AIBoxMQTaskResult(
    val url: String,
    val attributes: JsonNode?
)

/**
 * 任务状态
 */
enum class TaskStatus {
    /**
     * 已就绪
     */
    PREPARED,

    /**
     * 运行中
     */
    RUNNING,

    /**
     * 完成
     */
    COMPLETED,

    /**
     * 失败
     */
    FAILED,

    /**
     * 取消
     */
    CANCELLED;

    fun isFinished() = this == COMPLETED || this == FAILED || this == CANCELLED
}

```

## 8. 故障排查

### 8.1 常见问题

#### 消息发送失败
- **检查配置**: 验证 RocketMQ 连接配置
- **网络连通性**: 确认网络可达性
- **权限验证**: 检查访问密钥是否正确

#### 分布式锁问题
- **Redis连接**: 确认 Redis 服务可用
- **锁超时**: 检查是否存在死锁情况
- **锁粒度**: 验证锁的粒度是否合适

#### 状态不一致
- **事务边界**: 确认事务配置正确
- **并发控制**: 检查是否存在并发更新
- **事件时序**: 验证事件发布时序

## 9. 总结

本文档详细介绍了 Murmuration 系统中两种 RocketMQ 回调监听器的实现和集成方法。通过合理的架构设计、完善的错误处理机制和性能优化策略，这两个监听器能够可靠地处理不同业务场景下的任务状态通知需求。

在实际使用中，建议根据具体的业务需求选择合适的监听器模式，并严格按照最佳实践进行配置和部署，以确保系统的稳定性和可靠性。