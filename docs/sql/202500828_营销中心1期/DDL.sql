create table pose_group
(
    id                    bigint      not null comment '主键ID' primary key,
    pose_group_name       varchar(64) not null comment '姿势组名',
    application_scenarios varchar(64) not null comment '应用场景',
    as_enabled            tinyint     not null default 1 comment '是否启用',
    created_time          datetime    not null comment '创建时间',
    revised_time          datetime    not null comment '更新时间',
    creator_id            bigint unsigned not null comment '创建人',
    creator_name          varchar(64) not null comment '创建人名称',
    reviser_id            bigint unsigned not null comment '更新人',
    reviser_name          varchar(64) not null comment '更新人名称',
    organization_id       bigint      not null comment '组织ID',
    tenant_id             bigint      not null comment '租户ID'
) comment '姿势组';

create table pose
(
    id              bigint       not null comment '主键ID' primary key,
    image_url       varchar(255) not null comment '姿势url',
    prompt          text null comment '提示词',
    identify_status varchar(16)  not null default 'PROCESSING' comment '识别状态',
    pose_group_id   bigint       not null comment '姿势组id',
    created_time    datetime     not null comment '创建时间',
    revised_time    datetime     not null comment '更新时间',
    creator_id      bigint unsigned not null comment '创建人',
    creator_name    varchar(64)  not null comment '创建人名称',
    reviser_id      bigint unsigned not null comment '更新人',
    reviser_name    varchar(64)  not null comment '更新人名称',
    organization_id bigint       not null comment '组织ID',
    tenant_id       bigint       not null comment '租户ID'
) comment '姿势';

alter table pose
    add foreign key (pose_group_id) references pose_group (id);

create table ai_box_task
(
    id           bigint      not null comment '主键ID' primary key,
    task_code    varchar(32) not null comment '任务编码',
    finish_time  datetime    null comment '完成时间',
    status varchar(32) not null default 'PREPARED' comment '任务状态(PREPARED/RUNNING/COMPLETED/FAILED/CANCELLED/TIMEOUT)',
    created_time    datetime     not null comment '创建时间',
    revised_time    datetime     not null comment '更新时间',
    creator_id      bigint unsigned not null comment '创建人',
    creator_name    varchar(64)  not null comment '创建人名称',
    reviser_id      bigint unsigned not null comment '更新人',
    reviser_name    varchar(64)  not null comment '更新人名称'
) comment 'AIBox任务';

create table ai_box_task_task_instance_mapping
(
    task_instance_id bigint not null comment 'task_instance_id',
    ai_box_task_id   bigint not null comment 'aiBoxId',
    PRIMARY KEY (task_instance_id, ai_box_task_id)
) comment 'aiBox任务和task instance的一对多映射';

alter table ai_box_task_task_instance_mapping
    add foreign key (ai_box_task_id) references ai_box_task (id);

alter table ai_box_task_task_instance_mapping
    add foreign key (task_instance_id) references task_instance (id);

-- 修改约束名称以 uniq_ 为前缀
alter table ai_box_task_task_instance_mapping
    add constraint uniq_ai_box_task_task_instance_mapping
        unique (task_instance_id);

alter table ai_box_task
    add column task_source varchar(32) not null comment 'ai box任务来源';

alter table ai_box_task
    add column task_source_id varchar(64) null comment '任务来源ID';

alter table task_instance_result
    add column attributes varchar(2048) null comment '任务结果其余出参';