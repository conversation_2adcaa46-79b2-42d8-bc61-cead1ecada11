package team.aikero.murmuration.task.handler

import org.awaitility.Awaitility
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.common.enums.task.Resolution
import team.aikero.murmuration.common.req.task.AlibabaDistributionRequest
import team.aikero.murmuration.common.req.task.KontextPodRequest
import team.aikero.murmuration.common.req.task.SkcInfo
import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.common.req.task.ai_box.*
import team.aikero.murmuration.common.vo.*
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.util.CodeGenerator
import team.aikero.murmuration.core.util.CodeRule
import team.aikero.murmuration.core.workflow.entity.AIBoxTask
import team.aikero.murmuration.core.workflow.task.TaskManager
import team.aikero.murmuration.service.node.task.meitu.MeiTuParameterReq
import team.aikero.murmuration.service.node.task.meitu.cutouts.MeiTuCutoutsReq
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 任务管理器测试
 *
 * <AUTHOR>
 */
@Disabled("耗时太久了")
@SpringBootTest(classes = [MurmurationApplication::class])
class TaskManagerTest {

    /**
     * 测试创建任务
     */
    @Disabled
    @Test
    fun `test create task`(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.REALESRGAN,
            Ability.UPSCALE,
            UpScaleRequest(
                "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                Resolution.R_2K,
            ),
        )

        // 查询任务
        Awaitility.await()
            .atMost(35, TimeUnit.SECONDS)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(5, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }

    /**
     * 测试创建姿势裂变任务
     */
    @Test
    @Disabled
    fun kontextPodTest(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.COMFY_UI,
            Ability.POSTURAL_FISSION,
            KontextPodRequest(
                "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png",
                "正面站立，头微低，左手抬起轻触脖颈，右手自然下垂，整体姿势随性放松"
            ),
        )

        // 查询任务，要3-5分钟
        Awaitility.await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }


    /**
     * 测试创建美图抠图任务
     */
    @Test
    @Disabled
    fun meiTuCutoutsTest(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.MEI_TU,
            Ability.CUTOUTS,
            MeiTuCutoutsReq(
                "https://oss-datawork.oss-cn-hangzhou.aliyuncs.com/ai_images/server/UPscale/3693618586259454603.png",
                MeiTuParameterReq().apply {
                    nMask = false
                    modelType = 2
                    nbox = true
                }
            ),
        )

        // 查询任务
        Awaitility.await()
            .atMost(60, TimeUnit.SECONDS)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(5, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }

    /**
     * 测试超分（4k）任务
     */
    @Test
    fun fourKTest(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) =
        withSystemUser {
           
            val aiBoxTaskId = sql.save(
                AIBoxTask {
                    this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                    this.taskSource = TaskSource.AI_BOX
                }, SaveMode.INSERT_ONLY
            ).modifiedEntity.id
            
            taskManager.createTask(
                supplier = Supplier.REALESRGAN,
                ability = Ability.FOUR_K,
                request = FourKHandlerRequest(
                    imageUrl = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png"
                ),
                aiBoxTaskId = aiBoxTaskId
            )

            // 查询任务，要3-5分钟
            Awaitility.await()
                .atMost(6, TimeUnit.MINUTES)
                .pollDelay(5, TimeUnit.SECONDS)
                .pollInterval(10, TimeUnit.SECONDS)
                .until {
                    taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
                }
        }

    @Test
    fun `create hand repair task`(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) =
        withSystemUser {
           
            val aiBoxTaskId = sql.save(
                AIBoxTask {
                    this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                    this.taskSource = TaskSource.AI_BOX
                }, SaveMode.INSERT_ONLY
            ).modifiedEntity.id
            
            taskManager.createTask(
                supplier = Supplier.AIP,
                ability = Ability.AI_BOX_HAND_REPAIR,
                request = HandRepairHandlerRequest(
                    inputImage = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png",
                    count = 2
                ),
                aiBoxTaskId = aiBoxTaskId
            )

            Awaitility.await()
                .atMost(6, TimeUnit.MINUTES)
                .pollDelay(5, TimeUnit.SECONDS)
                .pollInterval(10, TimeUnit.SECONDS)
                .until {
                    taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
                }
        }

    @Test
    fun `create smart cutting head task`(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) =
        withSystemUser {
           
            val aiBoxTaskId = sql.save(
                AIBoxTask {
                    this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                    this.taskSource = TaskSource.AI_BOX
                }, SaveMode.INSERT_ONLY
            ).modifiedEntity.id
            
            taskManager.createTask(
                supplier = Supplier.AIP,
                ability = Ability.AI_BOX_SMART_CUTTING_HEAD,
                request = SmartCuttingHeadHandlerRequest(
                    image = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png",
                    topLoc = TopLoc.NECK,
                    size = CuttingSize.OneToOne,
                    count = 2
                ),
                aiBoxTaskId = aiBoxTaskId
            )

            Awaitility.await()
                .atMost(6, TimeUnit.MINUTES)
                .pollDelay(5, TimeUnit.SECONDS)
                .pollInterval(10, TimeUnit.SECONDS)
                .until {
                    taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
                }
        }

    @Test
    fun `create text edit task`(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) =
        withSystemUser {
            val aiBoxTaskId = sql.save(
                AIBoxTask {
                    this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                    this.taskSource = TaskSource.AI_BOX
                }, SaveMode.INSERT_ONLY
            ).modifiedEntity.id
            
            taskManager.createTask(
                supplier = Supplier.AIP,
                ability = Ability.AI_BOX_TEXT_EDIT,
                request = TextEditHandlerRequest(
                    image = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png",
                    userPrompt = "正面站立，头微低，左手抬起轻触脖颈，右手自然下垂，整体姿势随性放松",
                    count = 2
                ),
                aiBoxTaskId = aiBoxTaskId
            )

            Awaitility.await()
                .atMost(6, TimeUnit.MINUTES)
                .pollDelay(5, TimeUnit.SECONDS)
                .pollInterval(10, TimeUnit.SECONDS)
                .until {
                    taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
                }
        }

    /**
     * 测试换背景任务
     */
    @Test
    fun changeBackgroundTest(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) = withSystemUser {
        val aiBoxTaskId = sql.save(
            AIBoxTask {
                this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                this.taskSource = TaskSource.AI_BOX
            }, SaveMode.INSERT_ONLY
        ).modifiedEntity.id
        
        taskManager.createTask(
            supplier = Supplier.AIP,
            ability = Ability.CHANGE_BACKGROUND,
            request = ChangeBackgroundHandlerInputRequest(
                imageUrl = "https://oss.yunbanfang.cn/0451031d-6a74-4e05-ad9e-4f77205ebbf5/tiangong_9830f4592c96443793d3bc52a36d7b07.PNG",
                scenePicture = ScenePictureVo(
                    1L,
                    "https://chuangxin-oss-cdn.tiangong.tech/tiangong_ce53123fc62c4af58b7f466c0cf817ce.jpg",
                    null
                ),
                batchSize = 2
            ),
            aiBoxTaskId = aiBoxTaskId
        )

        // 查询任务，要3-5分钟
        Awaitility.await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
            }
    }

    @Test
    fun `create remove watermark task`(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) =
        withSystemUser {
            val aiBoxTaskId = sql.save(
                AIBoxTask {
                    this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                    this.taskSource = TaskSource.AI_BOX
                }, SaveMode.INSERT_ONLY
            ).modifiedEntity.id
            
            taskManager.createTask(
                supplier = Supplier.ZUO_TANG,
                ability = Ability.REMOVE_WATERMARK,
                request = RemoveWatermarkHandlerRequest(
                    imageUrl = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png"
                ),
                aiBoxTaskId = aiBoxTaskId
            )

            Awaitility.await()
                .atMost(6, TimeUnit.MINUTES)
                .pollDelay(5, TimeUnit.SECONDS)
                .pollInterval(10, TimeUnit.SECONDS)
                .until {
                    taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
                }
        }

    /**
     * 测试姿势裂变任务
     */
    @Test
    fun postureTest(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) = withSystemUser {
        val aiBoxTaskId = sql.save(
            AIBoxTask {
                this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                this.taskSource = TaskSource.AI_BOX
            }, SaveMode.INSERT_ONLY
        ).modifiedEntity.id
        
        taskManager.createTask(
            supplier = Supplier.AIP,
            ability = Ability.POSTURAL_FISSION,
            request = PostureFissionHandlerInputRequest(
                imageUrl = "https://oss-datawork-cdn.tiangong.tech/ai_images/server/comfyui/2025071111_3693618586259482663_7342012298610991105_00001_.png",
                posture = PostureVo(
                    1L,
                    "https://chuangxin-oss-cdn.tiangong.tech/tiangong_9159a2598c014d5fbd30cf7c52fb8d67.jpg",
                    "Standing facing forward, with the head slightly tilted to the right and looking directly at the camera. Both arms are naturally lowered, with hands relaxed and fingers slightly curled. The posture conveys a casual and relaxed atmosphere."
                )
            ),
            aiBoxTaskId = aiBoxTaskId,
        )

        // 查询任务，要3-5分钟
        Awaitility.await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
            }
    }

    /**
     * 测试tryon任务
     */
    @Test
    fun difyTryONTest(
        @Autowired taskManager: TaskManager,
        @Autowired sql: KSqlClient
    ) = withSystemUser {
        val aiBoxTaskId = sql.save(
            AIBoxTask {
                this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                this.taskSource = TaskSource.AI_BOX
            }, SaveMode.INSERT_ONLY
        ).modifiedEntity.id

        // Windows调用ImageInfoFetcher.doFetch会报错Invalid prefix or suffix
        taskManager.createTask(
            supplier = Supplier.AIP,
            ability = Ability.TRY_ON,
            request = TryOnHandlerInputRequest(
                modelImage = ModelReferenceImageVo(1L,
                    1L,
                    "11",
                    "https://chuangxin-oss-cdn.tiangong.tech/tiangong_8aa5b1a666c64a63b06928672d4397e3.jpg"),
                clothImageUrl = "https://chuangxin-oss-cdn.tiangong.tech/tiangong_a0da5dabd12c4e2ca7c5bee5436b2b65.jpg",
                styleInfo = StyleInfoVo(
                    styleCode = "WY01SX01-aB1455-01",
                    category = "03-0301-030108",
                    marketCode = "zd",
                    marketStyleCode = "Classic_xx",
                    categoryName=  "男装-上装类-T恤"
                ),
                usingMj = false,
                clothLength = ClothingType.UPPER,
                usingkontext = false,
//                targetModelFaceImgUrl = targetModelFaceImgUrl,//有模特图
                backgroundImage = BackgroundImageVo(
                    1L,
                    "11",
                    "https://chuangxin-oss-cdn.tiangong.tech/tiangong_e1273d02827f4820b563c36d3299f9ea.jpg"
                ),//有背景图
//                kontextBatchSize = handlerRequest.kontextBatchSize,
                tryonBatchSize = 2,
//                poseRange = handlerRequest.poseRange,
            ),
            aiBoxTaskId = aiBoxTaskId
        )

        // 查询任务，要3-5分钟
        Awaitility.await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                taskManager.queryAIBoxTaskStatus(aiBoxTaskId).isCompleted()
            }
    }
    /**
     * 测试创建任务
     */
    @Test
    fun `alibabaDistributionTest`(@Autowired taskManager: TaskManager) = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "alibaba_distribution"
        taskManager.createTask(
            bizId = bizId,
            bizType = bizType,
            Supplier.AIP,
            Ability.ALIBABA_DISTRIBUTION,
            AlibabaDistributionRequest(
                spuCarouselUrls = listOf(
                    "https://cbu01.alicdn.com/img/ibank/O1CN013kK9px1jt8nonzG8F_!!*************-0-cib.jpg",
                    "https://cbu01.alicdn.com/img/ibank/O1CN015Eje2X1jt8nolb1xy_!!*************-0-cib.jpg"
                ),
                spuDetailUrls= listOf("https://gss0.baidu.com/-4o3dSag_xI4khGko9WTAnF6hhy/zhidao/pic/item/2f738bd4b31c87018d2a5c002b7f9e2f0608ffbc.jpg"),
                skcInfos = listOf(SkcInfo("PS25082900001", "https://cbu01.alicdn.com/img/ibank/O1CN013iKR352EVYdcafRIE_!!*************-0-cib.jpg"),
                    SkcInfo("PS25082900002", "https://cbu01.alicdn.com/img/ibank/O1CN015bUNZF1MQX25NkmTh_!!*************-0-cib.jpg") ) // 根据实际需要填充 SKC 信息
            )
        )

        // 查询任务
        Awaitility.await()
            .atMost(6, TimeUnit.MINUTES)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                taskManager.queryTaskStatus(bizId, bizType).isCompleted()
            }
    }
}
