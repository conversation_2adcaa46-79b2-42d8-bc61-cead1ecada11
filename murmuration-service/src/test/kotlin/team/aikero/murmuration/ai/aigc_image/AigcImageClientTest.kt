package team.aikero.murmuration.ai.aigc_image

import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.isOss
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.aigcImage.AigcImageClient
import java.net.URI
import java.util.concurrent.TimeUnit

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class AigcImageClientTest(
    @Autowired val client: AigcImageClient,
    @Autowired val ossTemplate: OssTemplate,
) {

    /**
     * 贴图
     */
    @Test
    fun texture() {
        // 前景图
        val foregroundImageUrl = "https://chuangxin-oss-cdn.tiangong.tech/58b58a0ae2c348369a2ff156c2db77a2.png"
        // 背景图
        val backgroundImageUrl = "https://chuangxin-oss-cdn.tiangong.tech/tiangong_547e04c1d4f34330a614bdaf72814072.png"
        // 蒙版图
        val maskImageUrl = "https://oss-datawork.oss-cn-hangzhou.aliyuncs.com/ai_images/server/aigc_logo_loc/5089728158849654828_35229219d5c86807ac69eb3330fcd62d.png"

        // 创建任务
        val taskId = client.createTextureTask(
            foregroundImageUrl = foregroundImageUrl,
            backgroundImageUrl = backgroundImageUrl,
            maskImageUrl = maskImageUrl,
        )

        // 查询任务
        await()
            .atMost(6, TimeUnit.SECONDS)
            .pollDelay(1, TimeUnit.SECONDS)
            .pollInterval(200, TimeUnit.MILLISECONDS)
            .until {
                client.getTextureTaskResult(taskId).isCompleted()
            }

        // 验证结果
        val asyncResult = client.getTextureTaskResult(taskId) as TaskResult.Completed<String>
        val outputImageUrl = asyncResult.value
        val isValidOssUrl = ossTemplate.isOss(URI.create(outputImageUrl))
        assertThat(isValidOssUrl).isTrue()
    }
}
