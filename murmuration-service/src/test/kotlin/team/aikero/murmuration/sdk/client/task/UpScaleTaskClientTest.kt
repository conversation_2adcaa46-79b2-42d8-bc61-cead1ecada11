package team.aikero.murmuration.sdk.client.task

import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Awaitility.await
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.enums.NetworkCode
import team.aikero.blade.core.protocol.andData
import team.aikero.blade.util.json.parseJson
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.enums.task.Resolution
import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.core.workflow.entity.TaskStatus
import team.aikero.murmuration.event.listener.TaskStatusNotification
import team.aikero.murmuration.infra.rocketmq.AliyunRocketMQClient
import java.time.Duration
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource

@DisplayName("超分任务FeignClient测试")
@SpringBootTest(
    classes = [MurmurationApplication::class], 
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = [
        "server.port=8080",
        "domain.nest-api=http://localhost:8080",
    ]
)
class UpScaleTaskClientTest(
    @Autowired val feignClient: UpScaleTaskClient,
    @Autowired val sql: KSqlClient,
    @Autowired val rocketMQClient: AliyunRocketMQClient,
) {

    @Test
    @DisplayName("创建任务成功")
    fun createTaskSuccessfully() = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        val response = feignClient.createTask(
            bizId = bizId,
            bizType = bizType,
            request = UpScaleRequest(
                "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                Resolution.R_2K,
            )
        )
        assertThat(response.successful).isTrue()
        val taskId = response.data!!

        // 等待任务成功
        await()
            .atMost(30, TimeUnit.SECONDS)
            .pollDelay(2, TimeUnit.SECONDS)
            .pollInterval(2, TimeUnit.SECONDS)
            .until {
                rocketMQClient.getMessageByKey("$taskId") != null
            }

        // 拉取MQ消息
        val payload = rocketMQClient.getMessageByKey("$taskId")!!.parseJson<TaskStatusNotification>()
        assertThat(payload.taskId).isEqualTo(taskId)
        assertThat(payload.bizId).isEqualTo(bizId)
        assertThat(payload.bizType).isEqualTo(bizType)
        assertThat(payload.status).isEqualTo(TaskStatus.COMPLETED)
        assertThat(payload.results).hasSize(1)
    }
    
    @Test
    @DisplayName("同步执行任务成功")
    fun executeTaskSuccessfully() = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        val response = feignClient.executeTask(
            bizId = bizId,
            bizType = bizType,
            request = UpScaleRequest(
                "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                Resolution.R_2K,
            ),
            timeout = Duration.ofSeconds(30),
        )

        val results = response.andData()
        assertThat(results).hasSize(1)
    }
    
    @Test
    @DisplayName("同步执行任务超时")
    fun executeTaskTimeout() = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"

        val timer = TimeSource.Monotonic.markNow()
        val response = feignClient.executeTask(
            bizId = bizId,
            bizType = bizType,
            request = UpScaleRequest(
                "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                Resolution.R_2K,
            ),
            timeout = Duration.ofSeconds(2),
        )
        val cost = timer.elapsedNow()

        assertThat(cost).isLessThanOrEqualTo(5.seconds)
        assertThat(response.successful).isFalse()
        assertThat(response.code).isEqualTo(NetworkCode.TIMEOUT.code)
    }
}
