package team.aikero.murmuration.service.node.task.aip

/**
 * 算法调度平台能力
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
enum class AipAbility(val taskType: String, val modelName: String) {

    /**
     * 超分（无损）
     */
    UPSCALE("UPscale", "realesrgan"),

    /**
     * COMFY_UI
     */
    COMFY_UI("comfyui", "comfyui"),

    /**
     * COMFY_UI_GPT
     */
    COMFY_UI_GPT("comfyui", "comfyui_gpt"),

    /**
     * COMFY_UI_ONLINE_CPU
     */
    COMFY_UI_ONLINE_CPU("comfyui", "comfyui_online_cpu"),

    /**
     * 花型3.0-logo提取
     */
    PATTERN_EXTRACTION_V3("pattern_extraction_v3", "pattern30"),

    /**
     * 服装向量提取
     */
    CLOTH_FEAT_EXTRACT("cloth-feat-extract", "feat_extract"),

    /**
     * 图像姿势提取
     */
    IMAGE_POSE_EXTRACT("Image_extraction_pose_prompt", "dify"),

    /**
     * 手部修复
     */
    HAND_REPAIR("aigc-body-repair", "body_repair"),

    /**
     * 智能裁剪头像
     */
    SMART_CUTTING_HEAD("fg_cons_crop", "garment_crop"),

    /**
     * 文字编辑
     */
    TEXT_EDIT("kontext_text_edit", "dify"),

    /**
     * 超分（4k）
     */
    FOUR_K("4k", "realesrgan"),

    /**
     * Kontext换背景
     */
    CHANGE_BACKGROUND_KONTEXT("Change_background_kontext", "dify"),

    /**
     * Kontext姿势裂变
     */
    AIBOX_POSTURE_KONTEXT("Aibox_Posture_kontext", "dify"),

    /**
     * MJ+try on+换脸+换背景+裂变+超分
     */
    TRY_ON("MJ_try_on_change_background_super_reslution", "dify"),

    /**
     * AE上架属性转换
     */
    AE_PUTAWAY_ATTRIBUTE_CONVERT("ae_putaway_attribute_convert", "dify"),

    /**
     * SKC颜色识别
     */
    SKC_COLOR_IDENTIFICATION("skc_color_identification", "dify"),

    /**
     * 1688铺货（图包生成）
     */
    ALIBABA_DISTRIBUTION("alibaba_distribution", "dify"),

    /**
     * 商品标题SEO生成并过滤
     */
    PRODUCT_NAME_SEO("product_name_seo", "dify"),
}
