package team.aikero.murmuration.service.node.task.lingvisions.reimage

import team.aikero.murmuration.common.enums.task.BgColor
import team.aikero.murmuration.common.enums.task.GenerationMode
import team.aikero.murmuration.common.req.task.LingVisionsReimageRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.common.vo.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.SimpleImage
import java.math.BigDecimal

/**
 * 灵图裂变任务节点
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@NodeIdentifier(name = "灵图裂变", supplier = Supplier.LING_VISIONS_REIMAGE, ability = Ability.IMAGE_DERIVATION)
class LingVisionsReimageTaskNode: TaskNode<ImageListInput, LingVisionsReimageParameter, ImageListOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: ImageListInput, parameter: LingVisionsReimageParameter) {
        // 循环创建灵图裂变任务
        for (image in input.images) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.LING_VISIONS_REIMAGE,
                ability = Ability.IMAGE_DERIVATION,
                request = LingVisionsReimageRequest(
                    imageUrl = image.getUrl(),
                    n = parameter.n,
                    bgColor = parameter.bgColor,
                    aspectRatio = parameter.aspectRatio,
                    generationMode = parameter.generationMode,
                    similarity = parameter.similarity,
                ),
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val images = taskResults.map {
            SimpleImage(it.fetchUrl())
        }
        return ImageListOutput(images)
    }
}

data class LingVisionsReimageParameter(
    /**
     * 生图数量
     */
    @NodeProperties(name = "生图数量")
    val n: Int?,

    /**
     * 背景颜色
     */
    @NodeProperties(name = "背景颜色")
    val bgColor: BgColor?,

    /**
     * 图片比例
     */
    @NodeProperties(name = "图片比例")
    val aspectRatio: String?,

    /**
     * 生成模式
     */
    @NodeProperties(name = "生成模式")
    val generationMode: GenerationMode?,

    /**
     * 相似度
     */
    @NodeProperties(name = "相似度")
    val similarity: BigDecimal?,
): Parameter
