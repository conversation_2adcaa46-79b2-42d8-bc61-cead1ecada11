package team.aikero.murmuration.service.node.task.aip.productNameSeo

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.PRODUCT_NAME_SEO)
class ProductNameSeoExecutor(client: AipClient): AipExecutor<ProductNameSeoInput, ProductNameSeoOutput>(client) {
    override fun validateTaskOutput(output: ProductNameSeoOutput) {
        if (output.text.isEmpty()) {
            throw InvalidTaskOutputException("SEO结果为空")
        }
    }
}

data class ProductNameSeoInput(
    /**
     * 商品名称
     */
    @field:JsonProperty("product_name")
    val productName: String,

    /**
     * 商品分类
     */
    @field:JsonProperty("product_category")
    val productCategory: String,
    /**
     * 目标语言，en英文 zh中文
     */
    @field:JsonProperty("target_language")
    val targetLanguage: String = "en",
)

data class ProductNameSeoOutput(
    /**
     * SEO结果
     */
    val text: List<String>,
)
