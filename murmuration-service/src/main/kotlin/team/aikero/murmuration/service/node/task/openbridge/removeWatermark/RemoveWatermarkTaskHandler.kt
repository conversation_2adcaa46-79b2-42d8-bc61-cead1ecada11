package team.aikero.murmuration.service.node.task.openbridge.removeWatermark

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.ai_box.RemoveWatermarkHandlerRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.openbridge.OpenBridgeClient
import team.aikero.murmuration.service.utils.transferFrom
import java.net.URI

/**
 * 去除水印
 */
@TaskIdentifier(
    supplier = Supplier.ZUO_TANG,
    ability = Ability.REMOVE_WATERMARK
)
class RemoveWatermarkTaskHandler(
    private val openBridgeClient: OpenBridgeClient,
    private val ossTemplate: OssTemplate
) : TaskHandler<RemoveWatermarkHandlerRequest, String> {
    override fun create(request: RemoveWatermarkHandlerRequest): String {
        val taskId =
            openBridgeClient.createRemoveWaterMark(ossTemplate.transferFrom(request.imageUrl))
        return taskId
    }

    override fun query(
        request: RemoveWatermarkHandlerRequest,
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        val taskResult = openBridgeClient.queryRemoveWatermark(context)
        return taskResult.map {
            val transferred = ossTemplate.transferFrom(it.map(URI::create))
            transferred.map(TaskHandlerResult::image)
        }
    }
}
