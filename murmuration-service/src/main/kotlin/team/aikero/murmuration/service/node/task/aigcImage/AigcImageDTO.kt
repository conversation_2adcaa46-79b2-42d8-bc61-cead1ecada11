package team.aikero.murmuration.service.node.task.aigcImage

/**
 * 任务状态
 */
enum class TaskStatus(val value: String) {

    /**
     * 初始化
     */
    INIT("10"),

    /**
     * 已提交
     */
    SUBMITTED("20"),

    /**
     * 生成中
     */
    GENERATING("30"),

    /**
     * 取消
     */
    CANCELED("40"),

    /**
     * 已完成
     */
    COMPLETED("50"),

    /**
     * 已回调
     */
    CALLBACK("60"),

    /**
     * 失败
     */
    FAILED("90");

    companion object {
        fun fromValue(value: String): TaskStatus? {
            return TaskStatus.entries.firstOrNull { it.value == value }
        }
    }
}
