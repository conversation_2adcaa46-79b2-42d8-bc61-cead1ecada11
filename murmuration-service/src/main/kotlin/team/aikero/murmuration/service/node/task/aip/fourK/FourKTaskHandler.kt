package team.aikero.murmuration.service.node.task.aip.fourK

import team.aikero.murmuration.common.req.task.ai_box.FourKHandlerRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.utils.ImageUtil

/**
 * 超分（4k）任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.REALESRGAN,
    ability = Ability.FOUR_K
)
class FourKTaskHandler(
    val executor: FourKExecutor,
    val image: ImageUtil,
): TaskHandler<FourKHandlerRequest, String> {
    override fun create(request: FourKHandlerRequest): String {
        return executor.createTask(
            FourKInput(image.getUrl(request.imageUrl))
        )
    }

    override fun query(request:FourKHandlerRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            val resultImageUrl = it.resImgs.first()
            listOf(TaskHandlerResult.image(resultImageUrl))
        }
    }
}
