package team.aikero.murmuration.service.node.task.lazada

import jakarta.validation.constraints.Size
import team.aikero.murmuration.common.req.task.LazadaChangeBackgroundRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.common.vo.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.StyleImage


/**
 * Lazada 换背景任务节点
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@NodeIdentifier(name = "Lazada 换背景", supplier = Supplier.LAZADA, ability = Ability.CHANGE_BACKGROUND)
class LazadaChangeBackgroundTaskNode: TaskNode<LazadaChangeBackgroundInput, Parameter.Empty, LazadaChangeBackgroundOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: LazadaChangeBackgroundInput, parameter: Parameter.Empty) {
        if (input.backgroundCode == null) {
            return
        }

        for (image in input.images) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.LAZADA,
                ability = Ability.CHANGE_BACKGROUND,
                request = LazadaChangeBackgroundRequest(
                    imageUrl = image.getUrl(),
                    backgroundCode = input.backgroundCode,
                ),
                // 存储任务ID和输入图片的关系
                storage = image,
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): LazadaChangeBackgroundOutput {
        // 找回任务ID和输入图片的关系
        val storage = taskManager.readTaskStorage<StyleImage>(context.node.id)
        val images = taskResults.map {
            val image = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            // 将输入图片附带的信息继续传递到输出图片中，但替换其中的URL
            image.copy(imageUrl = it.fetchUrl())
        }
        return LazadaChangeBackgroundOutput(images)
    }
}

data class LazadaChangeBackgroundInput(
    /**
     * 背景图编码
     */
    @NodeProperties(name = "背景图编码")
    val backgroundCode: String?,
    /**
     * 款式图
     */
    @NodeProperties(name = "款式图")
    @field:Size(min = 1)
    val images: List<StyleImage>,
): Input

data class LazadaChangeBackgroundOutput(
    /**
     * 换背景后的款式图
     */
    @NodeProperties(name = "款式图")
    val images: List<StyleImage>,
): Output
