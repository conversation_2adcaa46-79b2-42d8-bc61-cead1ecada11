package team.aikero.murmuration.service.node.task.aip.skcColorIdentification

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.SKC_COLOR_IDENTIFICATION)
class SkcColorIdentificationExecutor(client: AipClient): AipExecutor<SkcColorIdentificationInput, SkcColorIdentificationOutput>(client) {
    override fun validateTaskOutput(output: SkcColorIdentificationOutput) {
        val errorMessages = mutableListOf<String>()
        if (output.colour.isBlank()) {
            errorMessages.add("colour 不能为空")
        }
        if (output.spuid.isBlank()) {
            errorMessages.add("spuid 不能为空")
        }
        if (output.skcid.isBlank()) {
            errorMessages.add("skcid 不能为空")
        }
        if (errorMessages.isNotEmpty()) {
            throw InvalidTaskOutputException(errorMessages.joinToString("; "))
        }
    }
}

data class SkcColorIdentificationInput(
    /**
     * 图片URL
     */
    val url: String,

    /**
     * 品类（内部品类，多级，用-拼接），参考：女装-上装类-卫衣帽衫
     */
    val pinlei: String,

    /**
     * 输出图片URL
     */
    @field:JsonProperty("output_url")
    val outputUrl: String?,

    /**
     * SPU ID（spu-code）参考：PG2508230005
     */
    val spuid: String,

    /**
     * SKC ID（skc-code）参考：PS250823000030
     */
    val skcid: String,

    /**
     * SKC名称, 原商品颜色名，参考：卡其色
     */
    @field:JsonProperty("skc_name")
    val skcName: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SkcColorIdentificationOutput(
    /**
     * 识别的颜色，参考：黑色
     */
    @field:JsonProperty("colour")
    val colour: String,

    /**
     * 输出图片URL，跟输入值相同
     */
    @field:JsonProperty("output_url")
    val outputUrl: String?,

    /**
     * SPU ID（spu-code）
     */
    @field:JsonProperty("spuid")
    val spuid: String,

    /**
     * SPU ID（spu-code）
     */
    @field:JsonProperty("skcid")
    val skcid: String,
)