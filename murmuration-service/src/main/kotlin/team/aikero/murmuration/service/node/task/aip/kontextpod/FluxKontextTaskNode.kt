package team.aikero.murmuration.service.node.task.aip.kontextpod

import jakarta.validation.constraints.Size
import team.aikero.murmuration.common.req.task.KontextPodRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.common.vo.SimpleTaskResult

import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.StyleImage
import kotlin.collections.map

/**
 * 姿势裂变节点
 */
@NodeIdentifier(name = "Kontext-姿势裂变", supplier = Supplier.COMFY_UI, ability = Ability.POSTURAL_FISSION)
class FluxKontextTaskNode : TaskNode<FLuxKontextNodeInput, Parameter.Empty, FluxKontextNodeOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: FLuxKontextNodeInput, parameter: Parameter.Empty) {
        // 循环创建姿势裂变任务，每张图姿势裂变n次，n = prompts.size()
        for ((index, image) in input.images.withIndex()) {
            for (prompt in input.prompts) {
                taskManager.createTask(
                    nodeInstanceId = context.node.id,
                    supplier = Supplier.COMFY_UI,
                    ability = Ability.POSTURAL_FISSION,
                    request = KontextPodRequest(
                        imageUrl = image.getUrl(),
                        prompt = prompt,
                    ),
                    // 存储任务ID和输入图片的关系
                    storage = image,
                    groupIndex = index.toLong(),
                )
            }
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): FluxKontextNodeOutput {
        // 找回任务ID和输入图片的关系
        val storage = taskManager.readTaskStorage<StyleImage>(context.node.id)
        val images = taskResults.map {
            val image = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            // 将输入图片附带的信息继续传递到输出图片中，但替换其中的URL
            image.copy(imageUrl = it.fetchUrl())
        }
        return FluxKontextNodeOutput(images)
    }
}

data class FLuxKontextNodeInput(
    /**
     * 参考图信息
     */
    @NodeProperties(name = "款式图")
    @field:Size(min = 1)
    var images: List<StyleImage>,

    /**
     * 提示词
     */
    @NodeProperties(name = "提示词")
    @field:Size(min = 1)
    var prompts: List<String>,
) : Input

data class FluxKontextNodeOutput(
    /**
     * 姿势裂变结果
     */
    @NodeProperties(name = "姿势裂变后款式图")
    val images: List<StyleImage>,
): Output
