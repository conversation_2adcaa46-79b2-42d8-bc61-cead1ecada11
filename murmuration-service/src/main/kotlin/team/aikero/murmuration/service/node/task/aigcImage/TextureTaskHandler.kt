package team.aikero.murmuration.service.node.task.aigcImage

import team.aikero.murmuration.common.req.task.TextureRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * 贴图任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@TaskIdentifier(
    supplier = Supplier.AIGC_IMAGE,
    ability = Ability.TEXTURE,
)
class TextureTaskHandler(val client: AigcImageClient): TaskHandler<TextureRequest, Long> {

    override fun create(request: TextureRequest): Long {
        return client.createTextureTask(
            foregroundImageUrl = request.foregroundImageUrl,
            backgroundImageUrl = request.backgroundImageUrl,
            maskImageUrl = request.maskImageUrl,
            rotationAngle = request.rotationAngle,
        )
    }

    override fun query(request: TextureRequest, context: Long): TaskResult<List<TaskHandlerResult>> {
        val taskResult = client.getTextureTaskResult(context)
        return taskResult.map { url ->
            listOf(TaskHandlerResult.image(url))
        }
    }
}
