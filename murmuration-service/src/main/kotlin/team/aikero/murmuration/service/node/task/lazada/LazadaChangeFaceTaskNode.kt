package team.aikero.murmuration.service.node.task.lazada

import jakarta.validation.constraints.Size
import team.aikero.murmuration.common.req.task.LazadaChangeFaceRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.common.vo.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.StyleImage
import kotlin.collections.map


/**
 * Lazada 换脸任务节点
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@NodeIdentifier(name = "<PERSON>zada 换脸", supplier = Supplier.LAZADA, ability = Ability.CHANGE_FACE)
class LazadaChangeFaceTaskNode : TaskNode<LazadaChangeFaceInput, Parameter.Empty, LazadaChangeFaceOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: LazadaChangeFaceInput, parameter: Parameter.Empty) {
        if (input.modelCode == null) {
            return
        }

        for (image in input.images) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.LAZADA,
                ability = Ability.CHANGE_FACE,
                request = LazadaChangeFaceRequest(
                    imageUrl = image.getUrl(),
                    modelCode = input.modelCode,
                ),
                // 存储任务ID和输入图片的关系
                storage = image,
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): LazadaChangeFaceOutput {
        // 找回任务ID和输入图片的关系
        val storage = taskManager.readTaskStorage<StyleImage>(context.node.id)
        val images = taskResults.map {
            val image = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            // 将输入图片附带的信息继续传递到输出图片中，但替换其中的URL
            image.copy(imageUrl = it.fetchUrl())
        }
        return LazadaChangeFaceOutput(images)
    }
}

data class LazadaChangeFaceInput(
    @NodeProperties(name = "模特图编码")
    val modelCode: String?,

    @NodeProperties(name = "款式图")
    @field:Size(min = 1)
    val images: List<StyleImage>,
) : Input

data class LazadaChangeFaceOutput(
    @NodeProperties(name = "款式图")
    val images: List<StyleImage>,
) : Output
