package team.aikero.murmuration.service.node.task.aip.alibabaDistribution

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier
import java.math.BigDecimal

@AipExecutorIdentifier(AipAbility.ALIBABA_DISTRIBUTION)
class AlibabaDistributionExecutor(client: AipClient): AipExecutor<AlibabaDistributionInput, AlibabaDistributionOutput>(client)

data class AlibabaDistributionInput(
    /**
     * SPU轮播图URLs
     */
    @field:JsonProperty("spu_carousel_urls")
    val spuCarouselUrls: String,

    /**
     * SPU详情图URLs
     */
    @field:JsonProperty("spu_detail_urls")
    val spuDetailUrls: String,

    /**
     * SKC图片URLs
     */
    @field:JsonProperty("skc_infos")
    val skcInfos: String,

)

data class AlibabaDistributionOutput(

    /**
     * SPU图片URL列表
     */
    @field:JsonProperty("spu_urls")
    val spuUrls: List<String>?=null,

    /**
     * 白底图URL列表
     */
    @field:JsonProperty("white_background_urls")
    val whiteBackgroundUrls: BackgroundInfo ?=null,

    /**
     * SKC图片URL列表
     */
    @field:JsonProperty("skc_infos")
    val skcInfos: List<SkcInfo>?=null,

    /**
     * 尺码表图片URL列表
     */
    @field:JsonProperty("sizechart_urls")
    val sizechartUrls: List<String>?=null,

    /**
     * 验证状态 (1表示有效)
     */
    val valid: Int
)

data class SkcInfo(
    /**
     * skc图
     */
    @field:JsonProperty("skc_url")
    var skcUrl : String,
    /**
     * 款式编号
     */
    @field:JsonProperty("design_code")
    var designCode : String
)

data class BackgroundInfo(
    /**
     * 1:1 尺寸图
     */
    @field:JsonProperty("spu_white_pic_1_1")
    var spuWhitePic11 : String,
    /**
     * 3:4 尺寸图
     */
    @field:JsonProperty("spu_white_pic_3_4")
    var spuWhitePic34 : String
)
