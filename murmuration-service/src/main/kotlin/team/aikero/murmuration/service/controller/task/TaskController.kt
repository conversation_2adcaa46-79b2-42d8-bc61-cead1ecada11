package team.aikero.murmuration.service.controller.task

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.retry.RetryCallback
import org.springframework.retry.policy.TimeoutRetryPolicy
import org.springframework.retry.support.RetryTemplate
import team.aikero.blade.core.enums.NetworkCode
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.failed
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.common.vo.SimpleTaskResult
import team.aikero.murmuration.core.scheduler.WorkStatus
import team.aikero.murmuration.core.workflow.entity.TaskInstance
import team.aikero.murmuration.core.workflow.entity.TaskStatus
import team.aikero.murmuration.core.workflow.entity.by
import team.aikero.murmuration.core.workflow.entity.toSimpleResults
import team.aikero.murmuration.core.workflow.event.TaskWork
import team.aikero.murmuration.core.workflow.task.TaskExecutor
import team.aikero.murmuration.service.vector.RetryableException
import java.time.Duration

/**
 * 所有基于KSP生成的TaskController的基类
 * 
 * <AUTHOR>
 * @since 2025-08-30
 */
open class TaskController(
    val sql: KSqlClient,
    val taskExecutor: TaskExecutor
) {

    /**
     * 同步执行任务
     * 
     * @param task 任务实例
     * @param timeout 超时时间
     * @return 任务结果
     */
    fun executeTask(task: TaskInstance, timeout: Duration): DataResponse<List<SimpleTaskResult>> {
        // 重试参数
        val fixedBackoff = timeout.dividedBy(20)
        val retryPolicy = TimeoutRetryPolicy(timeout.toMillis())
        val retryTemplate = RetryTemplate.builder()
            .fixedBackoff(fixedBackoff)
            .retryOn(RetryableException::class.java)
            .customPolicy(retryPolicy)
            .build()

        val taskWork = TaskWork(
            id = task.id, 
            supplier = task.supplier, 
            ability = task.ability,
        )

        try {
            retryTemplate.execute(RetryCallback<Unit, RetryableException> {
                val status = taskExecutor.execute(taskWork)
                when (status) {
                    WorkStatus.REDO -> throw RetryableException()
                    WorkStatus.FINISHED -> Unit
                }
            })
        }
        catch (_: RetryableException) {
            // 超时的情况
            taskExecutor.notifyTimeout(taskWork)
            return failed(NetworkCode.TIMEOUT)
        }

        // 完成或失败的情况
        val taskInstance = sql.findOneById(newFetcher(TaskInstance::class).by {
            status()
            results {
                task()
                url()
                attributes()
            }
        }, task.id)

        // 完成
        if (taskInstance.status == TaskStatus.COMPLETED) {
            val results = taskInstance.results.toSimpleResults()
            return ok(results)
        }

        // 失败
        if (taskInstance.status == TaskStatus.FAILED) {
            return failed("TASK_FAILED")
        }

        // 预期之外的状态
        return failed("UNEXPECTED_STATUS")
    }
}
