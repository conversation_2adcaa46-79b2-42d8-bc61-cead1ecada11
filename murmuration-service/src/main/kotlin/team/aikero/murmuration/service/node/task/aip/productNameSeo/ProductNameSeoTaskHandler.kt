package team.aikero.murmuration.service.node.task.aip.productNameSeo

import team.aikero.murmuration.common.req.task.ProductNameSeoRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * 商品标题SEO生成并过滤任务处理器
 */
@TaskIdentifier(supplier = Supplier.AIP, ability = Ability.PRODUCT_NAME_SEO)
class ProductNameSeoTaskHandler(val executor: ProductNameSeoExecutor) : TaskHandler<ProductNameSeoRequest, String> {
    override fun create(request: ProductNameSeoRequest): String {
        return executor.createTask(
            ProductNameSeoInput(
                productName = request.productName,
                productCategory = request.productCategory,
                targetLanguage = request.targetLanguage,
            )
        )
    }

    override fun query(request: ProductNameSeoRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map { it.text.map { title -> TaskHandlerResult.any(title) } }
    }
}
