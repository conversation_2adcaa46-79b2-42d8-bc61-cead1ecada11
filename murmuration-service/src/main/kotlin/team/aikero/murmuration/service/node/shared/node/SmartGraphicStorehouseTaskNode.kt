@file:Suppress("UNCHECKED_CAST")

package team.aikero.murmuration.service.node.shared.node

import cn.hutool.core.io.FileUtil
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.core.JdbcTemplate
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.*
import team.aikero.murmuration.service.node.shared.graphic.GraphicStorehouse
import team.aikero.murmuration.service.node.shared.graphic.GraphicStorehouseType
import team.aikero.murmuration.service.node.shared.graphic.GraphicStorehouseType.*
import team.aikero.murmuration.service.node.shared.graphic.imageId
import team.aikero.murmuration.service.node.shared.graphic.selected

/**
 * 图案库持久化的任务节点基类
 *
 * 主要目的是为了提供一套便于转换的 API，来辅助保存和兼容原有的图案库各种错综复杂的类型关系，为了避免全局污染 API，通过基类约束 API 适用范围
 *
 * //todo 不该用 TaskNode 和 抽象类实现，找时间抽个迭代重构为接口，通过多继承实现 API 约束
 *
 * <AUTHOR>
 */
abstract class SmartGraphicStorehouseTaskNode<I : Input, P : Parameter, O : Output> : TaskNode<I, P, O>() {

    @Autowired
    lateinit var sql: KSqlClient

    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    /**
     * 将 Image 列表转换为 GraphicImage 列表
     */
    fun List<Image>.toGraphicImage(targetType: GraphicStorehouseType = UPLOAD_IMAGE): List<GraphicImage> {
        // 当前场景暂时只判断首元素类型，后续有需要再改为精确类型判断
        return if (this.first() is GraphicImage) {
            return this as List<GraphicImage>
        } else {
            // 自动转换
            this.map {
                detectAndConvert(it.getUrl(), targetType, it)
            }.saveToGraphicImage()
        }
    }

    /**
     * 根据节点类型和上下文自动处理引用关系
     */
    fun detectAndConvert(
        imageUrl: String,
        targetType: GraphicStorehouseType,
        originImage: Image?,
    ): GraphicStorehouse {

        // 自动检测原类型
        val originGraphicImage = when (originImage) {
            is GraphicImage -> originImage.image
            is SimpleImage,
            is StyleImage,
            is TemplateImage,
            null -> null
        }

        val storehouse = GraphicStorehouse {
            this.imageUrl = imageUrl
            this.imageType = targetType
            this.originImage = originGraphicImage?.imageId
            this.originType = originGraphicImage?.originType ?: ORIGINAL_IMAGE
            this.imageFormat = FileUtil.extName(imageUrl)
            this.tenantId = CurrentUserHolder.get().tenantId

            // 核心逻辑：智能设置referId
            this.referId = calculateReferId(originGraphicImage, targetType)
        }

        return storehouse
    }

    /**
     * 转换为GraphicImage列表
     */
    fun List<GraphicStorehouse>.saveToGraphicImage(): List<GraphicImage> {
        return save().map { GraphicImage(it.imageId) }
    }

    /**
     * 批量存储
     */
    fun List<GraphicStorehouse>.save(): List<GraphicStorehouse> {
        val graphicStorehouses = sql.saveEntities(this, SaveMode.INSERT_ONLY).items.map { it.modifiedEntity }

        val imageIds = graphicStorehouses.map { it.imageId }
        syncEs(imageIds)
        return graphicStorehouses
    }

    /**
     * 对 refer 图进行选中操作
     */
    fun List<GraphicStorehouse>.referSelect(): List<GraphicStorehouse> {
        val referIds = mapNotNull { it.referId }

        if (referIds.isNotEmpty()) {

            sql.executeUpdate(GraphicStorehouse::class) {
                set(table.selected, true)
                where(table.imageId valueIn referIds)
            }

            syncEs(referIds)
        }
        return this
    }

    /**
     * 将图片放入 es 同步任务表
     */
    private fun syncEs(imageIds: List<Long>) {
        // 将图案放入 es 同步补偿表

        // 两次 Es 同步扫描之间，可能存在 id 重复，忽略失败
        runCatching {
            // 这个表有点特殊，jimmer 写法不知道为什么落不进，先用 jdbc 写入
            val batchArgs = imageIds.distinct().map { arrayOf(it) }
            jdbcTemplate.batchUpdate("insert into graphic_info_es_sync_task(image_id) values (?)", batchArgs)
        }
    }

    /**
     * 智能计算referId - 实现文档中的复杂逻辑
     *
     * 不可明说
     *
     * @param originImage 父图，上一张图
     * @param targetType 目标类型
     * @return
     */
    private fun calculateReferId(
        originImage: GraphicStorehouse?,
        targetType: GraphicStorehouseType
    ): Long? {

        return when (targetType) {

            // 源头图片类型通常referId为null
            ORIGINAL_IMAGE,
            UPLOAD_IMAGE,
            EXTERNAL_IMPORT,
            LOGO_IDENTIFY_IMAGE -> null
            // 超分 refer 取父图
            ENLARGE_IMAGE -> originImage?.imageId
            // 原图是超分图时继承父图 refer，不是超分图取父图
            TRANSPARENT_IMAGE, TRANSPARENT_SEG_IMAGE -> if (ENLARGE_IMAGE == originImage?.imageType) originImage.referId else originImage?.imageId

            // 其他图片继承父图 referId
            else -> originImage?.referId ?: originImage?.imageId
        }
    }
}
