package team.aikero.murmuration.service.node.task.openbridge

import com.fasterxml.jackson.annotation.JsonValue
import team.aikero.murmuration.service.node.task.youchuan.JobStatus

/**
 * 图片裁剪返参
 */
data class ImageCroppingRequestData(
    /**
     * 请求 ID
     * 用来识别唯一一次请求调用，API供应商生成
     */
    val requestId: String? = null,
    /**
     * 图片链接
     */
    val imageUrl: String? = null,
)

data class Response<T>(
    val successful: Boolean,
    val code: String,
    val message: String?,
    val data: T?,
)

data class ImageModerationData(val resultList: List<Result>) {
    /**
     * 检测结果
     */
    data class Result(
        val riskLevel: RiskLevel,
        val labelList: List<Label>?,
    )

    /**
     * 风险标签
     */
    data class Label(
        val label: String?,
        val confidence: String?,
        val description: String?,
    )

    /**
     * 风险等级
     */
    enum class RiskLevel(
        @JsonValue val value: String,
        val level: Int,
    ) {
        HIGH(value = "high", level = 3),
        MEDIUM(value = "medium", level = 2),
        LOW(value = "low", level = 1),
        NONE(value = "none", level = 0);

        fun isHigherThan(other: RiskLevel) = this.level > other.level
    }
}

data class RemoveWatermarkRequest(
    val imageUrl: String
)

data class RemoveWatermarkData(
    val taskId: String,
    val status: Status,
    val imageUrl: String?
) {
    /**
     * 状态
     */
    enum class Status(@JsonValue val value: Int) {
        /**
         * 排队中
         */
        QUEUEING(0),

        /**
         * 成功
         */
        SUCCESS(1),

        /**
         * 准备中
         */
        PREPARATION(2),

        /**
         * 等待中
         */
        WAITING(3),

        /**
         * 处理中
         */
        PROCESSING(4);

    }
}


data class ImageToVideoRequest(

    /**
     * prompt格式：imageUrl+空格+提示词
     */
    var prompt: String
)


data class ImageToVideoRequestData(
    val taskId: String,
    val status: JobStatus,
    val message: String,
    val imageList: List<ImageInfo>
) {
    data class ImageInfo(
        val imageUrl: String?,
        val failureMessage: String?
    )
}