package team.aikero.murmuration.service.node.task.lazada

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.common.req.task.LazadaTryOnRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.common.vo.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.Image
import team.aikero.murmuration.service.node.shared.StyleImage
import team.aikero.murmuration.service.node.shared.groupByKit

/**
 * <PERSON>zada 虚拟换衣任务节点
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@NodeIdentifier(name = "Lazada 虚拟换衣", supplier = Supplier.LAZADA, ability = Ability.TRY_ON)
class LazadaTryOnTaskNode : TaskNode<LazadaTryOnInput, LazadaTryOnParameter, LazadaTryOnOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: LazadaTryOnInput, parameter: LazadaTryOnParameter) {
        // 占坑匹配机制说明：
        // 同一个图案下，同一个图套的所有衣服，只能给一个模特穿
        // 先根据图案分组，每个组内部再根据图套分组
        val kits = input.clothingImages
            .groupBy { it.graphicImage }
            .mapValues { it.value.groupByKit() }
            .values
            .flatten()
        val modelImages = input.modelImages

        // 取两者的最小长度，保证不会越界
        val minSize = minOf(kits.size, modelImages.size)

        // 按顺序配对
        val pairs = (0 until minSize).map { i ->
            modelImages[i] to kits[i]
        }

        // 循环发起虚拟换衣任务
        for ((modelImage, kit) in pairs) {
            // 模特要和图套下的每一件衣服配对
            for (image in kit) {
                taskManager.createTask(
                    nodeInstanceId = context.node.id,
                    supplier = Supplier.LAZADA,
                    ability = Ability.TRY_ON,
                    request = LazadaTryOnRequest(
                        clothingImageUrl = image.getUrl(),
                        clothingImageType = parameter.clothingImageType,
                        modelImageUrl = modelImage.getUrl(),
                        n = parameter.n,
                    ),
                    // 存储任务ID和输入图片的关系
                    storage = image,
                )
            }
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): LazadaTryOnOutput {
        // 找回任务ID和输入图片的关系
        val storage = taskManager.readTaskStorage<StyleImage>(context.node.id)
        val images = taskResults.map {
            val image = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            // 将输入图片附带的信息继续传递到输出图片中，但替换其中的URL
            image.copy(imageUrl = it.fetchUrl())
        }
        return LazadaTryOnOutput(images)
    }
}

data class LazadaTryOnInput(
    /**
     * 款式图
     */
    @NodeProperties("款式图")
    val clothingImages: List<StyleImage>,

    /**
     * 模特图
     */
    @NodeProperties("模特图")
    val modelImages: List<Image>,
) : Input

data class LazadaTryOnParameter(
    /**
     * 生图数量
     */
    @NodeProperties("生图数量")
    @field:Min(1)
    @field:Max(4)
    val n: Int = 4,

    /**
     * 服装图类型
     */
    @NodeProperties("服装图类型")
    val clothingImageType: ClothingType = ClothingType.UPPER,
) : Parameter

data class LazadaTryOnOutput(
    /**
     * 换衣结果
     */
    @NodeProperties("换衣后款式图")
    val images: List<StyleImage>,
) : Output
