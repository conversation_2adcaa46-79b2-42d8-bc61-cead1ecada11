package team.aikero.murmuration.service.node.task.aip.aeAttributeConvert

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.AE_PUTAWAY_ATTRIBUTE_CONVERT)
class AeAttributeConvertExecutor(client: AipClient): AipExecutor<AeAttributeConvertInput, AeAttributeConvertOutput>(client) {
    override fun validateTaskOutput(output: AeAttributeConvertOutput) {
        if (output.id.isBlank()) {
            throw InvalidTaskOutputException("id 不能为空")
        }
        // 其他字段除了id外，工作流生成失败时可能为null，不做强制验证
    }
}

data class AeAttributeConvertInput(
    /**
     * 产品ID，参考：PG2508230008
     */
    val id: String,

    /**
     * 产品名称，参考：BE女装|2025秋季欧美外贸复古可拆卸Lulu卫衣短款收腰拉链外套女
     */
    @field:JsonProperty("product_name")
    val productName: String,

    /**
     * 产品类型，参考：女装-上装类-卫衣帽衫
     */
    @field:JsonProperty("product_type")
    val productType: String,

    /**
     * 产品信息，参考：主要下游销售地区2-中东；主面料成分-棉；主面料成分2含量-50%（含）-70%（不含）；主面料成分含量-30%（含）-50%（不含）；产品类别-卫衣/绒衫；工艺-蜡染做旧；是否跨境货源-是；袖型-落肩袖；跨境风格类型-个性街头；适用性别-女；面料名称-棉；颜色-咖啡色,深灰色,炒雪花；风格-欧美风；风格类型-街头潮人；上市年份/季节-2025年冬季；主要下游销售地区1-欧美；主面料成分2-聚酯纤维；厚薄-厚；品牌-BE THRIVED；图案-纯色；尺码-L,M,XL；款式-套头；流行元素-做旧；版型-修身型；组合形式-单件；衣长-短款(40cm<衣长≤50cm）；袖长-长袖；货号-N25301；门襟-拉链；领型-连帽
     */
    @field:JsonProperty("product_info")
    val productInfo: String,

    /**
     * 主图URL
     */
    @field:JsonProperty("master_img_url")
    val masterImgUrl: String,

    /**
     * Muse属性映射表URL
     */
    @field:JsonProperty("muse_attr_xlsx_url")
    val museAttrXlsxUrl: String,

    /**
     * AE批量模板URL
     */
    @field:JsonProperty("ae_template_xlsx_url")
    val aeTemplateXlsxUrl: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AeAttributeConvertOutput(
    /** spu_id 参考 PG2508230008*/
    @field:JsonProperty("id")
    val id: String,

    /** 商品名称 */
    @field:JsonProperty("name")
    val name: String?,

    /** 商品品类名 */
    @field:JsonProperty("type")
    val type: String?,

    /** 商品属性列表（原始 info 文本） */
    @field:JsonProperty("info")
    val info: String?,

    /**
     * 属性转换结果 - json(object)
     * 参考：{"衣门襟": "拉链","面料弹性": "无弹性","袖型": "落肩袖","编织工艺": "无纺","受众年龄": "<24岁","季节": "冬","厚薄": "厚(THICK)","装饰": "做旧","成分": "棉","是否全开襟": "不带(No)","是否带帽": "带帽(Yes)","面料类型": "混纺","图案元素": "纯色","风格": "欧美风","高度相关化学物质": "无","板型": "紧身","面料克重(卫衣毛衫)": "中等","尺码": "L, M, XL" }
     * */
    @field:JsonProperty("res_attr")
    val resAttr: Map<String, String>? // ← 由 object 直接映射
)