package team.aikero.murmuration.service.node.task.aip.aeAttributeConvert

import team.aikero.murmuration.common.req.task.AeAttributeConvertRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * AE上架属性转换任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.AE_PUTAWAY_ATTRIBUTE_CONVERT
)
class AeAttributeConvertTaskHandler(
    val executor: AeAttributeConvertExecutor,
): TaskHandler<AeAttributeConvertRequest, String> {
    override fun create(request: AeAttributeConvertRequest): String {
        return executor.createTask(
            AeAttributeConvertInput(
                id = request.id,
                productName = request.productName,
                productType = request.productType,
                productInfo = request.productInfo,
                masterImgUrl = request.masterImgUrl,
                museAttrXlsxUrl = request.museAttrXlsxUrl,
                aeTemplateXlsxUrl = request.aeTemplateXlsxUrl
            )
        )
    }

    override fun query(request: AeAttributeConvertRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            // 将转换结果作为属性返回，URL字段使用占位符
            listOf(TaskHandlerResult.any( it))
        }
    }
}
