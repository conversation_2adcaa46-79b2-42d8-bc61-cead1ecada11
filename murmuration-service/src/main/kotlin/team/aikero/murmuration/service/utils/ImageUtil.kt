package team.aikero.murmuration.service.utils

import cn.hutool.core.net.url.UrlBuilder
import cn.hutool.core.text.CharSequenceUtil.removePrefix
import cn.hutool.core.util.URLUtil.url
import okio.ByteString.Companion.encodeUtf8
import org.springframework.stereotype.Component
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.isOss
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.blade.util.io.Uris.download
import team.aikero.murmuration.service.component.ImageInfoFetcher
import team.aikero.murmuration.service.node.task.aip.kontextpod.AspectRatio
import tech.tiangong.fashion.aigc.image.common.enums.TextureImageTypeEnum.Companion.result
import java.math.BigDecimal
import java.math.RoundingMode
import java.net.URI
import kotlin.text.replaceAfterLast
import kotlin.text.substringAfterLast

@Component
class ImageUtil(
    private val imageInfoFetcher: ImageInfoFetcher,
    private val ossTemplate: OssTemplate
) {

    fun getAspectRatio(imageUrl: String): String {
        val (height, weight) = imageInfoFetcher.fetch(imageUrl)
        //比例 = 宽 / 高，找到最接近的比例
        val value = BigDecimal(weight).setScale(3, RoundingMode.HALF_UP) /
                BigDecimal(height)
        return AspectRatio.similarValue(value.setScale(3, RoundingMode.HALF_UP))
    }

    fun getUrl(imageUrl: String): String {
        return ossTemplate.transferFrom(imageUrl)
    }
}

/**
 * 下载文件并转储到 Oss
 */
fun OssTemplate.transferFrom(url: String): String {
    val uri = UrlBuilder.of(url).toURI()
    val file = uri.download()
    val secureKey = file.name.toLowercaseExtAndEncoderUri()
    return if (isOss(uri)) uri.toString() else upload(key = secureKey, file = file)
}

fun String.toLowercaseExtAndEncoderUri(): String {
    return replaceAfterLast('.', substringAfterLast('.').lowercase())
}