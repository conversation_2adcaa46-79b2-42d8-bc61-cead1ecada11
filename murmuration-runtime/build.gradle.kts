import java.util.*

plugins {
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.common.conf)
    alias(commonLibs.plugins.google.ksp)
}

dependencies {
    implementation(projects.murmurationCommon)

    implementation(commonLibs.blade.auth)
    // Cloud 组件
    implementation(commonLibs.blade.web.cloud.spring.boot.starter)
    implementation(commonLibs.blade.file.spring.boot.starter)
    // 缓存
    implementation(commonLibs.blade.data.redis.spring.boot.starter)
    // 协程
    implementation(commonLibs.kotlinx.coroutines.core)
    // ORM
    implementation(commonLibs.blade.jimmer.spring.boot.starter)
    runtimeOnly(commonLibs.mysql.connector)

    // tool
    implementation(commonLibs.hutool.all)
    implementation("org.eclipse.jetty:jetty-client:12.0.23")
    implementation("org.apache.rocketmq:rocketmq-client-java:5.0.8")
    implementation("com.aliyun:rocketmq20220801:3.1.3") {
        exclude(group = "pull-parser", module = "pull-parser")
    }
    implementation("team.aikero.pigeon:pigeon-sdk:0.0.4")
    implementation("tech.tiangong.aigc:aigc-image-sdk:0.0.4-RELEASE")
    implementation("io.opentelemetry:opentelemetry-api:1.44.1")
    implementation("io.opentelemetry:opentelemetry-extension-kotlin:1.44.1")
    implementation("tech.tiangong.bfg:bfg-sdk:3.1.4")

    testImplementation(commonLibs.blade.test.spring.boot.starter)
    testImplementation(springBootLibs.junit.junitJupiterEngine)
    testImplementation("org.awaitility:awaitility-kotlin:4.3.0")
    testRuntimeOnly(springBootLibs.junit.junitPlatformLauncher)

    ksp(commonLibs.jimmer.ksp)
    ksp(projects.murmurationKsp.murmurationKspEnum)
}

tasks.test {
    useJUnitPlatform()

    val envFile = rootProject.file(".env")
    if (envFile.exists()) {
        val properties = Properties()
        envFile.reader().use { properties.load(it) }
        properties.forEach {
            val key = it.key.toString()
            val value = it.value
            systemProperty(key, value)
        }
    }
}
