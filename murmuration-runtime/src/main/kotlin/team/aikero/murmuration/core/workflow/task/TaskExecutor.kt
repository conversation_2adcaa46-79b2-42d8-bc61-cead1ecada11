package team.aikero.murmuration.core.workflow.task

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.event.ErrorReportEvent
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.blade.util.spring.Springs
import team.aikero.murmuration.core.scheduler.Executor
import team.aikero.murmuration.core.scheduler.WorkStatus
import team.aikero.murmuration.core.workflow.event.TaskCompletedEvent
import team.aikero.murmuration.core.workflow.event.TaskFailedEvent
import team.aikero.murmuration.core.EventPublisher
import team.aikero.murmuration.core.workflow.entity.TaskInstance
import team.aikero.murmuration.core.workflow.entity.TaskInstanceDetail
import team.aikero.murmuration.core.workflow.entity.TaskInstanceDetailDraft
import team.aikero.murmuration.core.workflow.entity.TaskInstanceResult
import team.aikero.murmuration.core.workflow.entity.TaskStatus
import team.aikero.murmuration.core.workflow.entity.WorkflowStatus
import team.aikero.murmuration.core.workflow.entity.by
import team.aikero.murmuration.core.workflow.event.TaskWork
import team.aikero.murmuration.metadata.comment

/**
 * 任务执行器
 *
 * <AUTHOR>
 */
@Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
open class TaskExecutor(
    val taskHandlerRegistry: TaskHandlerRegistry,
    val sql: KSqlClient,
    val objectMapper: ObjectMapper,
    val eventPublisher: EventPublisher,
) : Executor<TaskWork> {

    /**
     * 执行任务
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun execute(task: TaskWork): WorkStatus = withSystemUser<WorkStatus> {
        @Suppress("UNCHECKED_CAST")
        val handler = taskHandlerRegistry.get(task.supplier, task.ability) as TaskHandler<Any, Any>
        val instance = sql.findOneById(newFetcher(TaskInstance::class).by {
            nodeInstance {
                workflowInstance {
                    status()
                }
            }
            status()
            detail {
                allScalarFields()
            }
        }, task.id)

        // 工作流实例被暂停的情况
        if (instance.nodeInstance?.workflowInstance?.status == WorkflowStatus.PAUSED) {
            return@withSystemUser WorkStatus.FINISHED
        }

        val taskDetail = instance.detail ?: throw IllegalStateException("缺失任务明细")
        val taskStatus = when (instance.status) {
            TaskStatus.PREPARED -> doWhenPrepared(handler, instance, taskDetail)
            TaskStatus.RUNNING -> doWhenRunning(handler, instance, taskDetail)
            else -> {
                log.info { "任务[${instance.id}]当前状态[${instance.status}]不可执行" }
                return@withSystemUser WorkStatus.FINISHED
            }
        }

        when (taskStatus) {
            TaskStatus.PREPARED, TaskStatus.RUNNING -> WorkStatus.REDO
            TaskStatus.COMPLETED -> {
                // 发布任务完成事件
                eventPublisher.publish(TaskCompletedEvent(task))
                WorkStatus.FINISHED
            }

            TaskStatus.FAILED -> {
                // 发布任务失败事件
                eventPublisher.publish(TaskFailedEvent(task))
                WorkStatus.FINISHED
            }

            else -> throw UnsupportedOperationException("非法的任务状态[${taskStatus}]")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun notifyFailed(task: TaskWork, ex: Exception) = withSystemUser {
        // 更新任务状态
        this.updateTaskStatusWithException(task.id, ex)
        // 发布任务失败事件
        eventPublisher.publish(TaskFailedEvent(task))
        // 错误上报
        Springs.publishEvent(ErrorReportEvent(
            source = "TaskExecutor",
            ex = ex,
            context = mapOf(
                "任务实例ID" to task.id,
                "节点实例ID" to task.nodeId,
                "供应商" to task.supplier.comment,
                "能力" to task.ability.comment,
            ),
        ))
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun notifyTimeout(task: TaskWork) = withSystemUser {
        // 更新任务状态
        this.updateTaskStatus(task.id, TaskStatus.FAILED) {
            this.error = objectMapper.writeValueAsString(TaskResult.InternalErrorType.TASK_TIMEOUT)
        }
        // 发布任务失败事件
        eventPublisher.publish(TaskFailedEvent(task))
    }

    private fun doWhenPrepared(handler: TaskHandler<Any, Any>, instance: TaskInstance, taskDetail: TaskInstanceDetail): TaskStatus {
        val taskId = instance.id
        val request = objectMapper.treeToValue(taskDetail.request, handler.requestType.java)

        // 创建任务
        val context = try {
            handler.create(request)
        }
        // 任务立即完成
        catch (ex: TaskImmediatelyDoneException) {
            completeTaskAndSaveResults(taskId, ex.results)
            return TaskStatus.COMPLETED
        }
        // 任务创建失败，需要重试
        catch (ex: TaskCreationRetryableException) {
            log.error { "任务创建失败, 需要重试: ${ex.reason?.toJson()}" }
            return TaskStatus.PREPARED
        }
        // 创建任务失败
        catch (ex: Exception) {
            log.error(ex) { "创建任务失败: $taskId" }
            this.updateTaskStatusWithException(taskId, ex)
            return TaskStatus.FAILED
        }

        // 更新任务状态为RUNNING
        this.updateTaskStatus(taskId, TaskStatus.RUNNING) {
            this.context = objectMapper.valueToTree(context)
        }

        return TaskStatus.RUNNING
    }

    private fun doWhenRunning(handler: TaskHandler<Any, Any>, instance: TaskInstance, taskDetail: TaskInstanceDetail): TaskStatus {
        val taskId = instance.id
        val jsonContext = taskDetail.context ?: throw IllegalStateException("缺失任务上下文: $taskId")
        val context = objectMapper.treeToValue(jsonContext, handler.contextType.java)
        val request = objectMapper.treeToValue(taskDetail.request, handler.requestType.java)

        // 查询任务
        val result = try {
            handler.query(request, context)
        } catch (ex: Exception) {
            log.error(ex) { "查询任务失败: $taskId" }
            this.updateTaskStatusWithException(taskId, ex)
            return TaskStatus.FAILED
        }

        // 更新任务状态
        return when (result) {
            is TaskResult.Running -> {
                this.updateTaskStatus(taskId, TaskStatus.RUNNING)
                TaskStatus.RUNNING
            }

            is TaskResult.Completed -> {
                completeTaskAndSaveResults(taskId, result.value)
                TaskStatus.COMPLETED
            }

            is TaskResult.Failed -> {
                this.updateTaskStatus(taskId, TaskStatus.FAILED) {
                    this.error = result.error?.let { objectMapper.writeValueAsString(it) }
                }
                TaskStatus.FAILED
            }
        }
    }

    /**
     * 完成任务并保存结果
     */
    private fun completeTaskAndSaveResults(taskId: Long, results: List<TaskHandlerResult>) {
        sql.save(TaskInstance {
            this.id = taskId
            this.status = TaskStatus.COMPLETED
            this.results = results.map {
                TaskInstanceResult {
                    this.url = it.url
                    this.attributes = it.attributes?.let { attribute ->
                        objectMapper.valueToTree(attribute)
                    }
                }
            }
        }) {
            setMode(SaveMode.UPDATE_ONLY)
            setAssociatedMode(TaskInstance::results, AssociatedSaveMode.APPEND)
        }
    }

    /**
     * 更新任务状态
     */
    private fun updateTaskStatus(taskId: Long, status: TaskStatus, block: (TaskInstanceDetailDraft.() -> Unit)? = null) {
        sql.save(TaskInstance {
            this.id = taskId
            this.status = status
            if (block != null) {
                detail(block)
            }
        }, SaveMode.UPDATE_ONLY, AssociatedSaveMode.UPDATE)
    }

    /**
     * 根据异常更新任务状态
     */
    private fun updateTaskStatusWithException(taskId: Long, ex: Exception) {
        try {
            this.updateTaskStatus(taskId, TaskStatus.FAILED) {
                this.error = ex.message
                this.errorStackTrace = ex.stackTraceToString()
            }
        }
        catch (ex: DataIntegrityViolationException) {
            log.error(ex) { "更新任务[$taskId]状态失败: 错误信息超长" }
            this.updateTaskStatus(taskId, TaskStatus.FAILED) {
                this.error = ex.message
                this.errorStackTrace = "错误信息超长"
            }
        }
    }
}
