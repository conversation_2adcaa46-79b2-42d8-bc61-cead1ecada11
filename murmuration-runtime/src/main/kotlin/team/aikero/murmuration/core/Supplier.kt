package team.aikero.murmuration.core

import team.aikero.murmuration.common.annotation.EnumComment

/**
 * 供应商
 *
 * 模型供应商、接口能力提供方
 *
 * <AUTHOR>
 */
@EnumComment
enum class Supplier {
    /**
     * 内置
     */
    MURMURATION,

    /**
     * 悠船
     */
    MIDJOURNEY,

    /**
     * 算法调度平台(realesrgan)模型
     */
    REALESRGAN,

    /**
     * 算法调度平台(comfyui)模型
     */
    COMFY_UI,

    /**
     * 算法调度平台
     */
    AIP,

    /**
     * 灵图裂变
     */
    LING_VISIONS_REIMAGE,

    /**
     * aigc-image能力
     */
    AIGC_IMAGE,

    /**
     * 美图
     */
    MEI_TU,

    /**
     * Lazada
     */
    LAZADA,

    /**
     * 潮际
     */
    CHAO_JI,

    /**
     * 佐糖
     */
    ZUO_TANG,
    /**
     * 阿里国际(Aidge)
     */
    AIDGE,

}
