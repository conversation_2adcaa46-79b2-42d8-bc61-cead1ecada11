package team.aikero.murmuration.core.workflow.entity

import com.fasterxml.jackson.databind.JsonNode
import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.Serialized
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.murmuration.common.vo.SimpleTaskResult

@Entity
interface TaskInstanceResult : LongId, CreatedTime {

    @ManyToOne
    val task: TaskInstance?

    /**
     * 图片URL（可选）
     * 当任务结果是图片时使用
     */
    val url: String?

    /**
     * 是否验收通过
     */
    @Default("false")
    val passed: Boolean

    /**
     * 任务结果属性（可选）
     * 当任务结果包含结构化数据时使用
     */
    @Serialized
    val attributes: JsonNode?
}

/**
 * TaskInstanceResult扩展函数
 */
fun TaskInstanceResult.toSimpleResult(): SimpleTaskResult {
    return SimpleTaskResult(
        taskId = this.task?.id!!,
        url = this.url,
        attributes = this.attributes
    )
}

/**
 * TaskInstanceResult列表扩展函数
 */
fun List<TaskInstanceResult>.toSimpleResults(): List<SimpleTaskResult> {
    return this.map { it.toSimpleResult() }
}
