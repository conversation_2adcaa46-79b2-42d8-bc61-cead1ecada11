package team.aikero.murmuration.event.listener

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.common.vo.SimpleTaskResult
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.TaskEvent
import team.aikero.murmuration.infra.rocketmq.Message
import team.aikero.murmuration.infra.rocketmq.RocketMQClient
import team.aikero.murmuration.infra.rocketmq.TopicHolder

/**
 * 监听 API 任务事件，发送 MQ 回调通知
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Component
class ApiTaskListener(
    val sql: KSqlClient,
    val objectMapper: ObjectMapper,
    val rocketMQClient: RocketMQClient,
) {

    /**
     * 任务完成事件
     */
    @EventListener
    fun onTaskCompleted(event: TaskEvent) = withSystemUser {
        // 只处理终结状态
        if (!event.status.isFinished()) {
            return@withSystemUser
        }

        val task = event.task
        val taskInstance = sql.findOneById(newFetcher(TaskInstance::class).by {
            bizId()
            bizType()
            triggerSource()
            status()
            ability()
            supplier()
            results {
                task()
                url()
                attributes()
            }
        }, task.id)

        // 只处理触发来源为API调用的任务
        if (taskInstance.triggerSource != TriggerSource.API) {
            return@withSystemUser
        }

        // 状态检查
        if (taskInstance.status != event.status) {
            throw IllegalStateException("任务实例[${taskInstance.id}]状态异常: 预期为[${event.status}], 实际为[${taskInstance.status}]")
        }

        // 消息体
        val messageBody = TaskStatusNotification(
            taskId = taskInstance.id,
            bizId = taskInstance.bizId
                ?: throw NullPointerException("当前任务实例[${taskInstance.id}]触发来源为API调用, 但业务ID为空"),
            bizType = taskInstance.bizType
                ?: throw NullPointerException("当前任务实例[${taskInstance.id}]触发来源为API调用, 但业务类型为空"),
            ability = taskInstance.ability,
            supplier = taskInstance.supplier,
            status = taskInstance.status,
            results = taskInstance.results.toSimpleResults()
        )

        val message = Message(
            topic = TopicHolder.topic,
            keys = listOf("${taskInstance.id}"),
            tag = taskInstance.bizType,
            payload = messageBody,
        )
        rocketMQClient.send(message)
    }
}

data class TaskStatusNotification(
    val taskId: Long,
    val bizId: String,
    val bizType: String,
    val ability: Ability,
    val supplier: Supplier,
    val status: TaskStatus,
    val results: List<SimpleTaskResult>,
)
