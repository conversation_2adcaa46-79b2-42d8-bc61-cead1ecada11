package team.aikero.murmuration.service.node.task.aip.postureKontext

import team.aikero.murmuration.common.req.task.ai_box.PostureFissionHandlerInputRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.utils.ImageUtil

/**
 * Kontext姿势裂变处理器
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.POSTURAL_FISSION
)
class PostureKontextTaskHandle(
    val executor: PostureKontextExecutor,
    private val imageUtil: ImageUtil
): TaskHandler<PostureFissionHandlerInputRequest, String> {
    override fun create(request: PostureFissionHandlerInputRequest): String {
        return executor.createTask(
            PostureKontextInput(
                imageUrl = imageUtil.getUrl(request.imageUrl),
                aspectRatio =  imageUtil.getAspectRatio(request.imageUrl),
                posturePrompt =  request.posture.prompt,
            )
        )
    }

    override fun query(request: PostureFissionHandlerInputRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            val resultImageUrl = it.resImgs.first()
            listOf(TaskHandlerResult.image(resultImageUrl))
        }
    }
}