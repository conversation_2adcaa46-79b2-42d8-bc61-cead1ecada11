package team.aikero.murmuration.service.node.task.aip.aibox.smart_cutting_head

import team.aikero.murmuration.common.req.task.ai_box.SmartCuttingHeadHandlerRequest
import team.aikero.murmuration.common.req.task.ai_box.SmartCuttingHeadRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.utils.ImageUtil

@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.AI_BOX_SMART_CUTTING_HEAD
)
class SmartCuttingHeadTaskHandler(
    private val executor: SmartCuttingHeadExecutor,
    private val imageUtil: ImageUtil
) : TaskHandler<SmartCuttingHeadHandlerRequest, String> {
    override fun create(request: SmartCuttingHeadHandlerRequest): String {
        return executor.createTask(
            SmartCuttingHeadInput(
                List(request.count) { imageUtil.getUrl(request.image) },
                request.topLoc.name.lowercase(),
                request.size.getSize()
            )
        )
    }

    override fun query(
        request: SmartCuttingHeadHandlerRequest,
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            it.resImgs.map(TaskHandlerResult::image)
        }
    }
}