package team.aikero.murmuration.service.component

import cn.hutool.core.net.url.UrlBuilder
import cn.hutool.core.net.url.UrlPath
import cn.hutool.core.util.URLUtil.url
import okio.ByteString.Companion.encodeUtf8
import org.apache.poi.util.LocaleID
import org.springframework.stereotype.Component
import team.aikero.blade.file.FileInfos.metadata
import team.aikero.blade.file.core.ImageMetadata
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.download
import team.aikero.blade.oss.OssTemplateExt.isOss
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.blade.util.io.Uris.download
import team.aikero.blade.util.io.Uris.filenameByUrl
import tech.tiangong.fashion.aigc.image.common.enums.TextureImageTypeEnum.Companion.result
import java.net.URI
import java.net.URL
import java.net.URLEncoder

/**
 * 图片信息提取器
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Component
class ImageInfoFetcher(val ossTemplate: OssTemplate) {

    /**
     * 获取图片信息
     *
     * @param imageUrl 图片URL
     * @return 图片信息
     */
    fun fetch(imageUrl: String): ImageInfo {
        return try {
            doFetch(imageUrl)
        }
        catch (ex: Exception) {
            throw ImageInfoFetchException(imageUrl, ex)
        }
    }

    private fun doFetch(imageUrl: String): ImageInfo =
        when (val metadata = ossTemplate.download(UrlBuilder.of(imageUrl).toURI()).metadata()) {
            is ImageMetadata -> {
                metadata.size?.let {
                    if (it > IMAGE_MAX_SIZE) {
                        throw IllegalArgumentException("图片大小${metadata.size}超出")
                    }
                }
                ImageInfo(metadata.height?.toInt()!!, metadata.width?.toInt()!!)
            }
            else -> throw IllegalArgumentException("不支持的文件类型 ${metadata.mediaType}")
        }


    companion object {
        private const val IMAGE_MAX_SIZE = 10 * 1024 * 1024L
    }

}

/**
 * 图片信息
 */
data class ImageInfo(
    val height: Int,
    val width: Int,
)

class ImageInfoFetchException(imageUrl: String, ex: Exception): Exception("获取图片[${imageUrl}]信息失败", ex)