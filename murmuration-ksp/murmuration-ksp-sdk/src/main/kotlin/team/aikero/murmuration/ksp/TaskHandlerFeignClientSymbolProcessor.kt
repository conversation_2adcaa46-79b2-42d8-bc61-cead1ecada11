package team.aikero.murmuration.ksp

import com.google.devtools.ksp.KspExperimental
import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.Resolver
import com.google.devtools.ksp.processing.SymbolProcessor
import com.google.devtools.ksp.processing.SymbolProcessorEnvironment
import com.google.devtools.ksp.processing.SymbolProcessorProvider
import com.google.devtools.ksp.symbol.KSAnnotated
import com.google.devtools.ksp.symbol.KSClassDeclaration
import java.io.OutputStream

fun OutputStream.appendText(str: String) {
    this.write(str.toByteArray())
}

class TaskHandlerFeignClientSymbolProcessor(
    val codeGenerator: CodeGenerator,
) : SymbolProcessor {
    lateinit var file: OutputStream
    var invoked = false

    fun log(content: String) {
        file.appendText("$content\n")
    }

    @OptIn(KspExperimental::class)
    override fun process(resolver: Resolver): List<KSAnnotated> {
        if (invoked) {
            return emptyList()
        }

        file = codeGenerator.createNewFile(Dependencies(false), "", "ksp-sdk", "log")
        log("TaskHandlerFeignClientSymbolProcessor invoked")

        resolver
            .getDeclarationsFromPackage("team.aikero.murmuration.common.req.task")
            .filterIsInstance<KSClassDeclaration>()
            .forEach(::generateFeignClient)

        invoked = true
        return emptyList()
    }

    private fun generateFeignClient(classDecl: KSClassDeclaration) {
        log("Generating Feign client for ${classDecl.qualifiedName?.asString()}")
        val requestClassName = classDecl.simpleName.asString() // 如 UpScaleRequest
        val clientName = "${requestClassName.removeSuffix("Request")}TaskClient"
        val packageName = "team.aikero.murmuration.sdk.client.task"
        val requestQualifiedName = classDecl.qualifiedName?.asString() ?: return

        val file = codeGenerator.createNewFile(
            Dependencies(false),
            packageName,
            clientName
        )

        file.appendText(
            """
            package $packageName

            import org.springframework.cloud.openfeign.FeignClient
            import org.springframework.web.bind.annotation.*
            import team.aikero.blade.core.annotation.feign.InnerFeign
            import team.aikero.blade.core.protocol.DataResponse
            import $requestQualifiedName
            import team.aikero.murmuration.common.vo.SimpleTaskResult
            import team.aikero.murmuration.sdk.Constants
            import java.time.Duration

            /**
             * <AUTHOR> by KSP
             */
            @InnerFeign
            @FeignClient(
                contextId = "$clientName",
                name = Constants.SERVICE_NAME,
                path = "${'$'}{Constants.CONTEXT_PATH}/task/${requestClassName.removeSuffix("Request").toKebabCase()}",
                url = "\${'$'}{domain.nest-api}"
            )
            interface $clientName {

                /**
                 * 创建任务
                 *
                 * @param bizId 业务ID
                 * @param bizType 业务类型
                 * @param request 任务请求
                 * @return 任务ID
                 */
                @PostMapping("/create-task")
                fun createTask(
                    @RequestParam bizId: String, 
                    @RequestParam bizType: String, 
                    @RequestBody request: $requestClassName,
                ): DataResponse<Long>
                
                /**
                 * 同步执行任务
                 *
                 * @param bizId 业务ID
                 * @param bizType 业务类型
                 * @param request 任务请求
                 * @param timeout 超时时间
                 * @return 任务结果
                 */
                @PostMapping("/execute-task")
                fun executeTask(
                    @RequestParam bizId: String,
                    @RequestParam bizType: String,
                    @RequestBody request: ${requestClassName},
                    @RequestParam timeout: Duration,
                ): DataResponse<List<SimpleTaskResult>>
            }
            
            """.trimIndent()
        )
    }

    internal fun String.toKebabCase(): String {
        // 驼峰转为 kebab-case
        return this
            .replace(Regex("([a-z])([A-Z])"), "$1-$2")
            .replace(Regex("([A-Z])([A-Z][a-z])"), "$1-$2")
            .lowercase()
    }
}

class TaskHandlerFeignClientSymbolProcessorProvider : SymbolProcessorProvider {
    override fun create(environment: SymbolProcessorEnvironment): SymbolProcessor {
        return TaskHandlerFeignClientSymbolProcessor(environment.codeGenerator)
    }
}
