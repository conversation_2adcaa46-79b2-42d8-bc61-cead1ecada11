package team.aikero.murmuration.ksp

import com.google.devtools.ksp.processing.*
import com.google.devtools.ksp.symbol.KSAnnotated
import com.google.devtools.ksp.symbol.KSClassDeclaration
import java.io.OutputStream

/**
 * 任务处理器 REST 控制器符号处理器
 *
 * 扫描使用 @TaskIdentifier 注解的 TaskHandler 类，自动生成对应的 REST Controller
 *
 * <AUTHOR>
 */
class TaskHandlerRestControllerSymbolProcessor(
    val codeGenerator: CodeGenerator,
) : SymbolProcessor {
    lateinit var file: OutputStream
    var invoked = false

    override fun process(resolver: Resolver): List<KSAnnotated> {
        if (invoked) {
            return emptyList()
        }

        file = codeGenerator.createNewFile(Dependencies(false), "", "ksp-service", "log")

        val taskHandlerFqName = "team.aikero.murmuration.core.workflow.task.TaskHandler"
        val taskIdentifierFqName = "team.aikero.murmuration.core.annotations.TaskIdentifier"
        val controllerPackage = "team.aikero.murmuration.service.controller.task.inner"

        resolver.getSymbolsWithAnnotation(taskIdentifierFqName)
            .filterIsInstance<KSClassDeclaration>()
            .forEach { classDecl ->
                // 1. 获取父类 TaskHandler<Req, Ctx>
                val superType = classDecl.superTypes
                    .map { it.resolve() }
                    .find { it.declaration.qualifiedName?.asString() == taskHandlerFqName }
                if (superType == null) return@forEach

                val typeArgs = superType.arguments
                val requestType = typeArgs.getOrNull(0)?.type?.resolve()!!

                // 获取类注释（只取第一行描述）
                val classComment = classDecl.docString?.trim()?.let { docString ->
                    // 提取第一行非空的描述内容，忽略 @author、@since 等标签
                    docString.lines()
                        .map { it.trim() }.firstOrNull { it.isNotEmpty() && !it.startsWith("@") } ?: ""
                } ?: ""

                // 2. 生成 Controller 类名和路径
                val handlerName = classDecl.simpleName.asString() // 如 ElementalDerivationTaskHandler
                val baseName = handlerName.removeSuffix("TaskHandler")
                val controllerName = "${baseName}TaskController"
                val requestMapping = "/inner/task/" + baseName.toKebabCase()
                val serviceName = classComment.ifEmpty { baseName }
                val requestTypeName = requestType.declaration.simpleName.asString()

                // 3. 生成 Controller 代码
                val file = codeGenerator.createNewFile(
                    Dependencies(false, classDecl.containingFile!!),
                    controllerPackage,
                    controllerName
                )
                file.bufferedWriter().use { writer ->
                    writer.write(
                        """
                        package $controllerPackage

                        import com.fasterxml.jackson.databind.ObjectMapper
                        import org.babyfish.jimmer.sql.kt.KSqlClient
                        import org.springframework.web.bind.annotation.*
                        import team.aikero.blade.core.protocol.DataResponse
                        import team.aikero.blade.core.protocol.ok
                        import team.aikero.murmuration.common.vo.SimpleTaskResult
                        import team.aikero.murmuration.core.EventPublisher
                        import team.aikero.murmuration.core.annotations.TaskIdentifier
                        import team.aikero.murmuration.core.workflow.task.TaskExecutor
                        import team.aikero.murmuration.core.workflow.task.TaskManager
                        import team.aikero.murmuration.service.controller.task.TaskController
                        import ${classDecl.qualifiedName?.asString()}
                        import ${requestType.declaration.qualifiedName?.asString()}
                        import java.time.Duration
                        import kotlin.reflect.full.findAnnotation

                        /**
                         * ${serviceName}-内部服务
                         *
                         * <AUTHOR> by KSP
                         */
                        @RestController
                        @RequestMapping("$requestMapping")
                        class $controllerName(
                            sql: KSqlClient,
                            taskExecutor: TaskExecutor,
                            val objectMapper: ObjectMapper,
                            val handler: $handlerName,
                            val eventPublisher: EventPublisher,
                            val taskManager: TaskManager,
                        ): TaskController(sql, taskExecutor) {

                            /**
                             * 创建任务
                             *
                             * @param bizId 业务ID
                             * @param bizType 业务类型
                             * @param request 任务请求
                             * @return 任务ID
                             */
                            @PostMapping("/create-task")
                            fun createTask(
                                @RequestParam bizId: String,
                                @RequestParam bizType: String,
                                @RequestBody request: ${requestTypeName},
                            ): DataResponse<Long> {
                                val identifier = handler::class.findAnnotation<TaskIdentifier>()
                                    ?: throw IllegalStateException("任务处理器[${'$'}{handler::class}]缺少`@TaskIdentifier`注解")

                                // 持久化任务实例
                                val taskId = taskManager.createTask(
                                    bizId = bizId,
                                    bizType = bizType,
                                    supplier = identifier.supplier,
                                    ability = identifier.ability,
                                    request = request,
                                )

                                return ok(taskId)
                            }
                            
                            /**
                             * 同步执行任务
                             * 
                             * @param bizId 业务ID
                             * @param bizType 业务类型
                             * @param request 任务请求
                             * @param timeout 超时时间
                             * @return 任务结果
                             */
                            @PostMapping("/execute-task")
                            fun executeTask(
                                @RequestParam bizId: String,
                                @RequestParam bizType: String,
                                @RequestBody request: ${requestTypeName},
                                @RequestParam timeout: Duration,
                            ): DataResponse<List<SimpleTaskResult>> {
                                val identifier = handler::class.findAnnotation<TaskIdentifier>()
                                    ?: throw IllegalStateException("任务处理器[${'$'}{handler::class}]缺少`@TaskIdentifier`注解")

                                // 持久化任务实例
                                val savedTask = taskManager.saveTask(
                                    bizId = bizId,
                                    bizType = bizType,
                                    supplier = identifier.supplier,
                                    ability = identifier.ability,
                                    request = request,
                                )
                                
                                return executeTask(savedTask, timeout)
                            }
                        }
                        
                        """.trimIndent()
                    )
                }
            }


        invoked = true
        return emptyList()
    }

    internal fun String.toKebabCase(): String {
        // 驼峰转为 kebab-case
        return this
            .replace(Regex("([a-z])([A-Z])"), "$1-$2")
            .replace(Regex("([A-Z])([A-Z][a-z])"), "$1-$2")
            .lowercase()
    }

}


class TaskHandlerRestControllerSymbolProcessorProvider : SymbolProcessorProvider {
    override fun create(environment: SymbolProcessorEnvironment): SymbolProcessor {
        return TaskHandlerRestControllerSymbolProcessor(environment.codeGenerator)
    }
}
